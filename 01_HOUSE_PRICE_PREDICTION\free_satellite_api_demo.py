#!/usr/bin/env python3
"""
Free Satellite Image API Demo
No payment or API keys required!

This script demonstrates how to fetch real satellite images using free services
as an alternative to Google Maps API and Mapbox.
"""

import requests
import math
import io
from PIL import Image
import matplotlib.pyplot as plt
import numpy as np

def deg2num(lat_deg, lon_deg, zoom):
    """Convert latitude/longitude to tile coordinates"""
    lat_rad = math.radians(lat_deg)
    n = 2.0 ** zoom
    x = int((lon_deg + 180.0) / 360.0 * n)
    y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
    return (x, y)

def get_openstreetmap_tile(lat, lon, zoom=18):
    """
    Get satellite-style tile from OpenStreetMap
    - 100% Free
    - Global coverage
    - No registration required
    """
    x, y = deg2num(lat, lon, zoom)
    url = f"https://tile.openstreetmap.org/{zoom}/{x}/{y}.png"
    
    try:
        response = requests.get(url, timeout=10, headers={
            'User-Agent': 'House Price Prediction Project'
        })
        if response.status_code == 200:
            return Image.open(io.BytesIO(response.content))
    except Exception as e:
        print(f"OpenStreetMap error: {e}")
    return None

def get_usgs_satellite(lat, lon, zoom=18):
    """
    Get satellite imagery from USGS National Map
    - 100% Free
    - US coverage only
    - High quality
    - No registration required
    """
    x, y = deg2num(lat, lon, zoom)
    url = f"https://basemap.nationalmap.gov/arcgis/rest/services/USGSImageryOnly/MapServer/tile/{zoom}/{y}/{x}"
    
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            return Image.open(io.BytesIO(response.content))
    except Exception as e:
        print(f"USGS error: {e}")
    return None

def get_esri_world_imagery(lat, lon, zoom=18):
    """
    Get satellite imagery from ESRI World Imagery
    - 100% Free
    - Global coverage
    - High quality
    - No registration required
    """
    x, y = deg2num(lat, lon, zoom)
    url = f"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{zoom}/{y}/{x}"
    
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            return Image.open(io.BytesIO(response.content))
    except Exception as e:
        print(f"ESRI error: {e}")
    return None

def get_satellite_image_multi_source(lat, lon, zoom=18):
    """
    Try multiple free sources with fallback
    Returns the first successful image
    """
    sources = [
        ("ESRI World Imagery", get_esri_world_imagery),
        ("USGS Satellite", get_usgs_satellite),
        ("OpenStreetMap", get_openstreetmap_tile)
    ]
    
    for source_name, source_func in sources:
        print(f"Trying {source_name}...")
        image = source_func(lat, lon, zoom)
        if image is not None:
            print(f"✅ Success with {source_name}")
            return image, source_name
    
    print("❌ All sources failed")
    return None, None

def extract_basic_features_from_real_image(image):
    """
    Extract computer vision features from real satellite image
    Same as synthetic but adapted for real images
    """
    if image is None:
        return {}
    
    # Convert to numpy array
    img_array = np.array(image.convert('RGB'))
    
    # Convert to HSV for better color analysis
    from PIL import Image as PILImage
    img_hsv = np.array(image.convert('HSV'))
    
    features = {}
    
    # Basic color analysis
    features['avg_red'] = np.mean(img_array[:, :, 0])
    features['avg_green'] = np.mean(img_array[:, :, 1])
    features['avg_blue'] = np.mean(img_array[:, :, 2])
    
    # Green space detection (vegetation)
    # HSV values for green vegetation
    green_mask = (
        (img_hsv[:, :, 0] > 60) & (img_hsv[:, :, 0] < 180) &  # Hue: green range
        (img_hsv[:, :, 1] > 50) &  # Saturation: not too gray
        (img_hsv[:, :, 2] > 50)    # Value: not too dark
    )
    features['green_space_ratio'] = np.sum(green_mask) / img_array.size
    
    # Blue space detection (water, pools)
    blue_mask = (
        (img_hsv[:, :, 0] > 180) & (img_hsv[:, :, 0] < 240) &  # Hue: blue range
        (img_hsv[:, :, 1] > 100) &  # High saturation
        (img_hsv[:, :, 2] > 100)    # Bright
    )
    features['blue_space_ratio'] = np.sum(blue_mask) / img_array.size
    features['pool_detected'] = 1 if features['blue_space_ratio'] > 0.005 else 0
    
    # Built area detection (buildings, roads)
    gray_mask = (
        (img_hsv[:, :, 1] < 50) &  # Low saturation (gray)
        (img_hsv[:, :, 2] > 100)   # Bright enough
    )
    features['built_area_ratio'] = np.sum(gray_mask) / img_array.size
    
    # Texture analysis
    gray_img = np.array(image.convert('L'))
    features['brightness_std'] = np.std(gray_img)
    features['contrast'] = np.max(gray_img) - np.min(gray_img)
    
    return features

def demo_free_satellite_apis():
    """
    Demonstrate free satellite API usage
    """
    print("🛰️ FREE SATELLITE IMAGE API DEMO")
    print("=" * 50)
    
    # Example coordinates (you can change these)
    test_locations = [
        {"name": "New York City", "lat": 40.7128, "lon": -74.0060},
        {"name": "Los Angeles", "lat": 34.0522, "lon": -118.2437},
        {"name": "Chicago", "lat": 41.8781, "lon": -87.6298},
    ]
    
    for location in test_locations:
        print(f"\n📍 Testing location: {location['name']}")
        print(f"   Coordinates: {location['lat']}, {location['lon']}")
        
        # Try to get satellite image
        image, source = get_satellite_image_multi_source(
            location['lat'], location['lon'], zoom=16
        )
        
        if image is not None:
            # Extract features
            features = extract_basic_features_from_real_image(image)
            
            print(f"   📊 Features extracted:")
            print(f"     • Green space ratio: {features.get('green_space_ratio', 0):.3f}")
            print(f"     • Built area ratio: {features.get('built_area_ratio', 0):.3f}")
            print(f"     • Pool detected: {'Yes' if features.get('pool_detected', 0) else 'No'}")
            print(f"     • Image contrast: {features.get('contrast', 0):.1f}")
            
            # Save image for inspection
            filename = f"satellite_{location['name'].replace(' ', '_').lower()}.png"
            image.save(filename)
            print(f"   💾 Image saved as: {filename}")
        else:
            print(f"   ❌ Failed to get satellite image")

def integrate_with_house_price_project():
    """
    Show how to integrate with your house price prediction project
    """
    print("\n🏠 INTEGRATION WITH HOUSE PRICE PROJECT")
    print("=" * 50)
    
    # Example house data
    house_data = {
        'latitude': 40.7128,   # You would get this from your dataset
        'longitude': -74.0060,  # You would get this from your dataset
        'GrLivArea': 2000,
        'OverallQual': 8,
        'YearBuilt': 2010
    }
    
    print(f"🏡 Analyzing house at: {house_data['latitude']}, {house_data['longitude']}")
    
    # Get satellite image
    image, source = get_satellite_image_multi_source(
        house_data['latitude'], house_data['longitude']
    )
    
    if image is not None:
        # Extract satellite features
        satellite_features = extract_basic_features_from_real_image(image)
        
        # Combine with house features
        combined_features = {**house_data, **satellite_features}
        
        print(f"✅ Combined features for ML model:")
        for key, value in combined_features.items():
            if isinstance(value, float):
                print(f"   • {key}: {value:.3f}")
            else:
                print(f"   • {key}: {value}")
        
        # This is where you would feed into your ML model
        print(f"\n🤖 These features can now be used in your ML model!")
        print(f"   Total features: {len(combined_features)}")
        print(f"   Satellite source: {source}")
    else:
        print("❌ Could not get satellite image - would use synthetic generation")

if __name__ == "__main__":
    print("🚀 Starting Free Satellite API Demo...")
    
    # Run the demo
    demo_free_satellite_apis()
    
    # Show integration example
    integrate_with_house_price_project()
    
    print("\n🎉 Demo complete!")
    print("\n📋 Summary:")
    print("   ✅ No API keys required")
    print("   ✅ No payment needed")
    print("   ✅ Multiple free sources available")
    print("   ✅ Ready for integration with your project")
    print("\n💡 Your current synthetic approach is already excellent!")
    print("   This demo shows how to add real satellite data if desired.")
