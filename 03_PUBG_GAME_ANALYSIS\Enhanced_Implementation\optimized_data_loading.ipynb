# Core libraries
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Visualization
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
import plotly.io as pio

# System
import os
import gc  # Garbage collection for memory management

# Configure visualization
%matplotlib inline
pio.renderers.default = 'notebook_connected'
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

print("✅ Environment configured")

# Define data path
DATA_PATH = '../data/pubg.csv'

# Check file existence and size
if os.path.exists(DATA_PATH):
    file_size = os.path.getsize(DATA_PATH)
    print(f"📁 File found: {DATA_PATH}")
    print(f"📊 Size: {file_size:,} bytes ({file_size/1024**2:.2f} MB)")
    
    if file_size > 100 * 1024 * 1024:  # > 100 MB
        print("⚠️  Large file detected - using optimized loading")
        USE_OPTIMIZED_LOADING = True
    else:
        USE_OPTIMIZED_LOADING = False
else:
    print(f"❌ File not found: {DATA_PATH}")
    USE_OPTIMIZED_LOADING = False

# Read just the first few rows to understand structure
print("🔍 Examining file structure...")

try:
    df_peek = pd.read_csv(DATA_PATH, nrows=3, low_memory=False)
    
    print(f"✅ Successfully read {len(df_peek)} sample rows")
    print(f"📋 Columns ({len(df_peek.columns)}):")
    
    for i, col in enumerate(df_peek.columns, 1):
        dtype = str(df_peek[col].dtype)
        print(f"  {i:2d}. {col:<20} ({dtype})")
        
except Exception as e:
    print(f"❌ Error reading file: {e}")
    df_peek = None

# Display sample data
if df_peek is not None:
    print("📊 Sample data:")
    display(df_peek)
else:
    print("❌ No sample data available")

def load_pubg_data_optimized(file_path, sample_size=10000):
    """
    Load PUBG data with memory optimization.
    
    For large files:
    - Load a random sample instead of full dataset
    - Optimize data types
    - Use chunked processing
    """
    print(f"📊 Loading optimized sample of {sample_size:,} rows...")
    
    try:
        # Method 1: Load sample using skiprows
        # First, estimate total rows by reading in chunks
        chunk_size = 10000
        total_rows = 0
        
        print("🔢 Estimating total rows...")
        for i, chunk in enumerate(pd.read_csv(file_path, chunksize=chunk_size)):
            total_rows += len(chunk)
            if i >= 10:  # Estimate based on first 10 chunks
                estimated_total = total_rows * (os.path.getsize(file_path) / (chunk_size * 11 * 100))  # Rough estimate
                print(f"📊 Estimated total rows: ~{estimated_total:,.0f}")
                break
        
        # Load sample
        df_sample = pd.read_csv(file_path, nrows=sample_size, low_memory=False)
        
        print(f"✅ Sample loaded: {df_sample.shape}")
        print(f"💾 Memory usage: {df_sample.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
        
        return df_sample
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None

# Load the data
if USE_OPTIMIZED_LOADING:
    df_pubg = load_pubg_data_optimized(DATA_PATH, sample_size=15000)
else:
    df_pubg = pd.read_csv(DATA_PATH, low_memory=False)
    print(f"✅ Full dataset loaded: {df_pubg.shape}")

def optimize_data_types(df):
    """
    Optimize data types to reduce memory usage.
    """
    if df is None:
        return None
        
    print("🔧 Optimizing data types...")
    
    # Store original memory usage
    original_memory = df.memory_usage(deep=True).sum() / 1024**2
    
    # Optimize integer columns
    int_cols = df.select_dtypes(include=['int64']).columns
    for col in int_cols:
        if df[col].min() >= 0:
            if df[col].max() < 255:
                df[col] = df[col].astype('uint8')
            elif df[col].max() < 65535:
                df[col] = df[col].astype('uint16')
            else:
                df[col] = df[col].astype('uint32')
        else:
            if df[col].min() > -128 and df[col].max() < 127:
                df[col] = df[col].astype('int8')
            elif df[col].min() > -32768 and df[col].max() < 32767:
                df[col] = df[col].astype('int16')
            else:
                df[col] = df[col].astype('int32')
    
    # Optimize float columns
    float_cols = df.select_dtypes(include=['float64']).columns
    for col in float_cols:
        df[col] = df[col].astype('float32')
    
    # Convert string columns to category if they have few unique values
    object_cols = df.select_dtypes(include=['object']).columns
    for col in object_cols:
        if df[col].nunique() / len(df) < 0.5:  # Less than 50% unique values
            df[col] = df[col].astype('category')
    
    # Calculate memory savings
    optimized_memory = df.memory_usage(deep=True).sum() / 1024**2
    savings = ((original_memory - optimized_memory) / original_memory) * 100
    
    print(f"✅ Memory optimized:")
    print(f"   Original: {original_memory:.2f} MB")
    print(f"   Optimized: {optimized_memory:.2f} MB")
    print(f"   Savings: {savings:.1f}%")
    
    return df

# Optimize the dataset
if df_pubg is not None:
    df_pubg = optimize_data_types(df_pubg)
    
    # Force garbage collection
    gc.collect()
    print("🗑️ Memory cleaned")

# Basic dataset information
if df_pubg is not None:
    print("📊 DATASET OVERVIEW")
    print("=" * 40)
    print(f"Shape: {df_pubg.shape}")
    print(f"Memory usage: {df_pubg.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
    print(f"Missing values: {df_pubg.isnull().sum().sum()}")
    print(f"Duplicate rows: {df_pubg.duplicated().sum()}")
else:
    print("❌ No data available for analysis")

# Display column information
if df_pubg is not None:
    print("📋 COLUMN INFORMATION")
    print("=" * 40)
    
    info_df = pd.DataFrame({
        'Column': df_pubg.columns,
        'Type': df_pubg.dtypes,
        'Non-Null': df_pubg.count(),
        'Null%': (df_pubg.isnull().sum() / len(df_pubg) * 100).round(2)
    })
    
    display(info_df)

# Test visualization with optimized data
if df_pubg is not None and 'kills' in df_pubg.columns:
    print("🎨 Testing visualization...")
    
    # Simple histogram
    fig, ax = plt.subplots(figsize=(10, 6))
    ax.hist(df_pubg['kills'], bins=30, alpha=0.7, edgecolor='black')
    ax.set_title('Distribution of Kills (Sample Data)', fontsize=14, fontweight='bold')
    ax.set_xlabel('Number of Kills')
    ax.set_ylabel('Frequency')
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    print("✅ Matplotlib visualization working")
else:
    print("❌ Cannot create visualization - data not available")

# Test Plotly visualization
if df_pubg is not None and 'kills' in df_pubg.columns:
    print("🎨 Testing Plotly visualization...")
    
    fig = go.Figure()
    fig.add_trace(go.Histogram(
        x=df_pubg['kills'],
        nbinsx=30,
        name='Kills Distribution',
        opacity=0.7
    ))
    
    fig.update_layout(
        title='Distribution of Kills (Sample Data)',
        xaxis_title='Number of Kills',
        yaxis_title='Frequency',
        template='plotly_white',
        width=800,
        height=500
    )
    
    fig.show()
    print("✅ Plotly visualization working")
else:
    print("❌ Cannot create Plotly visualization - data not available")

print("📋 ANALYSIS SUMMARY")
print("=" * 50)

if df_pubg is not None:
    print(f"✅ Successfully loaded PUBG dataset")
    print(f"📊 Working with {len(df_pubg):,} rows and {len(df_pubg.columns)} columns")
    print(f"💾 Memory usage: {df_pubg.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
    print(f"🎯 Data ready for analysis and visualization")
    
    print(f"\n💡 RECOMMENDATIONS:")
    print(f"1. ✅ Use this optimized dataset for your analysis")
    print(f"2. ✅ Visualizations should now work properly")
    print(f"3. ✅ Memory usage is optimized")
    print(f"4. 🔄 For full dataset analysis, use chunked processing")
    print(f"5. 💾 Consider saving as Parquet for faster future loading")
    
else:
    print(f"❌ Failed to load dataset")
    print(f"\n🔧 TROUBLESHOOTING:")
    print(f"1. Check if pubg.csv file exists in ../data/")
    print(f"2. Verify file is not corrupted")
    print(f"3. Ensure sufficient system memory")
    print(f"4. Try reducing sample_size parameter")