#!/usr/bin/env python3
"""
Large CSV File Handler for PUBG Dataset
Handles memory-efficient loading and analysis of large CSV files
"""

import pandas as pd
import numpy as np
import os
import sys
from pathlib import Path

class LargeCSVHandler:
    """Handle large CSV files efficiently"""
    
    def __init__(self, csv_path='pubg.csv'):
        self.csv_path = csv_path
        self.file_size = None
        self.total_rows = None
        self.columns = None
        
    def check_file_info(self):
        """Get basic file information"""
        if not os.path.exists(self.csv_path):
            print(f"❌ Error: {self.csv_path} not found!")
            return False
            
        self.file_size = os.path.getsize(self.csv_path)
        print(f"📁 File: {self.csv_path}")
        print(f"📊 Size: {self.file_size:,} bytes ({self.file_size/1024**2:.2f} MB)")
        return True
    
    def peek_structure(self, nrows=5):
        """Peek at file structure without loading everything"""
        print(f"\n🔍 Examining first {nrows} rows...")
        
        try:
            # Read just a few rows to understand structure
            df_peek = pd.read_csv(self.csv_path, nrows=nrows, low_memory=False)
            
            self.columns = df_peek.columns.tolist()
            
            print(f"✅ Successfully read {len(df_peek)} rows")
            print(f"📋 Columns ({len(self.columns)}):")
            
            for i, col in enumerate(self.columns, 1):
                dtype = str(df_peek[col].dtype)
                print(f"  {i:2d}. {col:<20} ({dtype})")
            
            print(f"\n📈 Sample data:")
            print(df_peek.head(3).to_string())
            
            return df_peek
            
        except Exception as e:
            print(f"❌ Error reading file: {e}")
            return None
    
    def count_rows_efficiently(self):
        """Count total rows efficiently"""
        print(f"\n🔢 Counting total rows...")
        
        try:
            # Method 1: Using pandas read_csv with chunksize
            chunk_size = 10000
            total_rows = 0
            
            for chunk in pd.read_csv(self.csv_path, chunksize=chunk_size):
                total_rows += len(chunk)
                if total_rows % 100000 == 0:
                    print(f"   Processed {total_rows:,} rows...")
            
            self.total_rows = total_rows
            print(f"✅ Total rows: {total_rows:,}")
            return total_rows
            
        except Exception as e:
            print(f"❌ Error counting rows: {e}")
            return None
    
    def load_sample(self, sample_size=10000, random_state=42):
        """Load a random sample for analysis"""
        print(f"\n📊 Loading random sample of {sample_size:,} rows...")
        
        try:
            # If we know total rows, sample randomly
            if self.total_rows:
                skip_rows = sorted(np.random.choice(
                    range(1, self.total_rows + 1), 
                    size=self.total_rows - sample_size, 
                    replace=False
                ))
                df_sample = pd.read_csv(self.csv_path, skiprows=skip_rows)
            else:
                # Otherwise, just take first N rows
                df_sample = pd.read_csv(self.csv_path, nrows=sample_size)
            
            print(f"✅ Sample loaded: {df_sample.shape}")
            return df_sample
            
        except Exception as e:
            print(f"❌ Error loading sample: {e}")
            return None
    
    def load_in_chunks(self, chunk_size=10000):
        """Generator to load file in chunks"""
        print(f"\n🔄 Loading file in chunks of {chunk_size:,} rows...")
        
        try:
            chunk_iter = pd.read_csv(self.csv_path, chunksize=chunk_size)
            return chunk_iter
            
        except Exception as e:
            print(f"❌ Error creating chunk iterator: {e}")
            return None
    
    def analyze_sample(self, df):
        """Analyze a sample dataframe"""
        if df is None:
            return
            
        print(f"\n📊 SAMPLE ANALYSIS")
        print("=" * 50)
        
        print(f"Shape: {df.shape}")
        print(f"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
        
        print(f"\nMissing values:")
        missing = df.isnull().sum()
        if missing.sum() > 0:
            print(missing[missing > 0])
        else:
            print("No missing values found")
        
        print(f"\nNumerical columns summary:")
        numerical_cols = df.select_dtypes(include=[np.number]).columns
        if len(numerical_cols) > 0:
            print(df[numerical_cols].describe())
        
        print(f"\nCategorical columns:")
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns
        for col in categorical_cols:
            unique_count = df[col].nunique()
            print(f"  {col}: {unique_count} unique values")
            if unique_count <= 10:
                print(f"    Values: {df[col].unique()}")

def main():
    """Main function to analyze PUBG CSV"""
    print("🎮 PUBG CSV FILE ANALYZER")
    print("=" * 50)
    
    # Initialize handler
    handler = LargeCSVHandler('pubg.csv')
    
    # Check file
    if not handler.check_file_info():
        return
    
    # Peek at structure
    df_peek = handler.peek_structure(nrows=3)
    if df_peek is None:
        return
    
    # Count rows (this might take a while for large files)
    print(f"\n⚠️  Row counting may take time for large files...")
    user_input = input("Count total rows? (y/n): ").lower().strip()
    
    if user_input == 'y':
        handler.count_rows_efficiently()
    
    # Load and analyze sample
    df_sample = handler.load_sample(sample_size=5000)
    if df_sample is not None:
        handler.analyze_sample(df_sample)
    
    print(f"\n✅ Analysis complete!")
    
    # Provide recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    print("1. Use chunked processing for full dataset analysis")
    print("2. Sample data for visualization (5K-10K rows)")
    print("3. Use data types optimization to reduce memory")
    print("4. Consider using Parquet format for faster loading")

if __name__ == "__main__":
    main()
