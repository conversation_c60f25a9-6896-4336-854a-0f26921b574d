# ===== KAFKA STREAMING CODE ONLY (NO MODEL CREATION) =====
# Copy this code into Section 42 - assumes model exists from previous sections

import subprocess
import sys
import warnings
import json
import time
import threading
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from IPython.display import display, HTML, clear_output
import logging

# Suppress all warnings and Kafka connection errors
warnings.filterwarnings('ignore')
logging.getLogger('kafka').setLevel(logging.CRITICAL)
logging.getLogger('kafka.conn').setLevel(logging.CRITICAL)
logging.getLogger('kafka.client').setLevel(logging.CRITICAL)

print("🚀 APACHE KAFKA REAL-TIME STREAMING")
print("=" * 50)

# ==================== KAFKA INSTALLATION & CHECK ====================
def install_and_check_kafka():
    """Install and check Kafka-Python"""
    kafka_status = {
        'kafka_python_installed': False,
        'kafka_server_available': False,
        'connection_mode': 'fallback'
    }
    
    # Install kafka-python if needed
    try:
        import kafka
        print("✅ kafka-python library is installed")
        kafka_status['kafka_python_installed'] = True
    except ImportError:
        try:
            print("📦 Installing kafka-python...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                "kafka-python==2.0.2", "--quiet", "--upgrade"
            ])
            import kafka
            print("✅ kafka-python installed successfully!")
            kafka_status['kafka_python_installed'] = True
        except Exception as e:
            print(f"⚠️ kafka-python installation failed: {e}")
            return kafka_status
    
    # Check Kafka server connection (suppress connection errors)
    try:
        from kafka import KafkaProducer
        from kafka.errors import NoBrokersAvailable

        # Suppress Kafka logging during connection test
        kafka_logger = logging.getLogger('kafka')
        original_level = kafka_logger.level
        kafka_logger.setLevel(logging.CRITICAL)

        try:
            # Test connection with very short timeout
            test_producer = KafkaProducer(
                bootstrap_servers='localhost:9092',
                request_timeout_ms=1000,  # Very short timeout
                retries=0,  # No retries
                api_version=(0, 10, 1),
                max_block_ms=1000  # Don't block for long
            )
            test_producer.close()

            kafka_status['kafka_server_available'] = True
            kafka_status['connection_mode'] = 'real_kafka'
            print("✅ Kafka server is running at localhost:9092")

        finally:
            # Restore original logging level
            kafka_logger.setLevel(original_level)

    except (NoBrokersAvailable, Exception):
        print("⚠️ Kafka server not available - using simulation mode")
        print("� This is normal if Kafka server is not running")
    
    return kafka_status

# Check Kafka status
kafka_status = install_and_check_kafka()

# Display connection info
print("\n📊 KAFKA CONNECTION STATUS")
print("-" * 30)
for key, value in kafka_status.items():
    print(f"{key}: {value}")

# ==================== KAFKA STREAMING CLASS ====================
class KafkaRealTimeStreaming:
    """Kafka streaming class for real-time data processing"""
    
    def __init__(self, bootstrap_servers='localhost:9092'):
        self.bootstrap_servers = bootstrap_servers
        self.use_kafka = kafka_status['kafka_server_available']
        
        # Kafka components
        self.producer = None
        self.consumer = None
        self.admin_client = None
        
        # Fallback components
        self.message_queue = []
        self.queue_lock = threading.Lock()
        self.subscribers = {}
        
        # Metrics
        self.messages_produced = 0
        self.messages_consumed = 0
        self.start_time = datetime.now()
        
        # Initialize streaming
        self._initialize_streaming()
    
    def _initialize_streaming(self):
        """Initialize Kafka or fallback streaming"""
        if self.use_kafka:
            self._initialize_kafka()
        else:
            self._initialize_fallback()
    
    def _initialize_kafka(self):
        """Initialize real Kafka connection"""
        try:
            from kafka import KafkaProducer, KafkaConsumer
            from kafka.admin import KafkaAdminClient

            # Suppress Kafka logging during initialization
            kafka_logger = logging.getLogger('kafka')
            original_level = kafka_logger.level
            kafka_logger.setLevel(logging.CRITICAL)

            try:
                self.producer = KafkaProducer(
                    bootstrap_servers=self.bootstrap_servers,
                    value_serializer=lambda v: json.dumps(v, default=str).encode('utf-8'),
                    request_timeout_ms=2000,  # Shorter timeout
                    retries=0,  # No retries
                    api_version=(0, 10, 1),
                    max_block_ms=2000  # Don't block for long
                )

                self.admin_client = KafkaAdminClient(
                    bootstrap_servers=self.bootstrap_servers,
                    request_timeout_ms=2000,
                    api_version=(0, 10, 1)
                )

                print("✅ Real Kafka connection established!")

            finally:
                # Restore original logging level
                kafka_logger.setLevel(original_level)

        except Exception:
            print("⚠️ Kafka initialization failed - using simulation mode")
            self.use_kafka = False
            self._initialize_fallback()
    
    def _initialize_fallback(self):
        """Initialize fallback streaming"""
        print("🔧 Using in-memory streaming simulation")
        print("💡 Perfect for demonstration without Kafka server!")
    
    def create_topic(self, topic_name, num_partitions=3, replication_factor=1):
        """Create Kafka topic or register fallback topic"""
        if self.use_kafka:
            try:
                from kafka.admin import NewTopic
                
                topic = NewTopic(
                    name=topic_name,
                    num_partitions=num_partitions,
                    replication_factor=replication_factor
                )
                
                self.admin_client.create_topics([topic], timeout_s=10)
                print(f"✅ Kafka topic '{topic_name}' created!")
                return True
                
            except Exception as e:
                if "TopicExistsError" in str(e):
                    print(f"ℹ️ Topic '{topic_name}' already exists")
                    return True
                else:
                    print(f"❌ Failed to create topic: {e}")
                    return False
        else:
            if topic_name not in self.subscribers:
                self.subscribers[topic_name] = []
            print(f"📝 Fallback topic '{topic_name}' registered")
            return True
    
    def produce_message(self, topic, message_data):
        """Produce message to Kafka or fallback queue"""
        enhanced_message = {
            **message_data,
            'timestamp': datetime.now().isoformat(),
            'message_id': f"msg_{self.messages_produced}",
            'topic': topic
        }
        
        if self.use_kafka:
            try:
                future = self.producer.send(topic, enhanced_message)
                record_metadata = future.get(timeout=5)
                
                self.messages_produced += 1
                print(f"📤 Kafka: Message {self.messages_produced} → '{topic}'")
                return True
                
            except Exception as e:
                print(f"❌ Kafka send failed: {e}")
                return self._fallback_produce(topic, enhanced_message)
        else:
            return self._fallback_produce(topic, enhanced_message)
    
    def _fallback_produce(self, topic, message):
        """Fallback message production"""
        with self.queue_lock:
            self.message_queue.append(message)
            self.messages_produced += 1
        
        print(f"📤 Simulation: Message {self.messages_produced} → '{topic}'")
        
        # Notify subscribers immediately in fallback mode
        if topic in self.subscribers:
            for callback in self.subscribers[topic]:
                try:
                    callback(message)
                    self.messages_consumed += 1
                except Exception as e:
                    print(f"❌ Callback error: {e}")
        
        return True
    
    def subscribe_to_topic(self, topic, callback_function):
        """Subscribe to topic messages"""
        if topic not in self.subscribers:
            self.subscribers[topic] = []
        self.subscribers[topic].append(callback_function)
        print(f"👂 Subscribed to topic '{topic}'")
    
    def get_streaming_metrics(self):
        """Get streaming metrics"""
        duration = datetime.now() - self.start_time
        
        return {
            'Mode': 'Apache Kafka' if self.use_kafka else 'In-Memory Simulation',
            'Messages Produced': self.messages_produced,
            'Messages Consumed': self.messages_consumed,
            'Duration (seconds)': round(duration.total_seconds(), 2),
            'Messages/Second': round(self.messages_produced / max(duration.total_seconds(), 1), 2),
            'Kafka Brokers': self.bootstrap_servers if self.use_kafka else 'N/A'
        }
    
    def display_metrics(self):
        """Display metrics in Jupyter-friendly format"""
        metrics = self.get_streaming_metrics()
        
        # Create DataFrame for display
        metrics_df = pd.DataFrame(list(metrics.items()), columns=['Metric', 'Value'])
        
        print("\n📊 STREAMING METRICS")
        print("=" * 40)
        display(metrics_df)
        
        return metrics
    
    def close(self):
        """Close connections"""
        if self.producer:
            self.producer.close()
        print("🔌 Streaming connections closed")

# ==================== INITIALIZE KAFKA STREAMING ====================
print("\n🔧 INITIALIZING KAFKA STREAMING SYSTEM")
print("-" * 40)

# Initialize Kafka streaming
kafka_streaming = KafkaRealTimeStreaming()

# Create topics for different data streams
topics_to_create = [
    'house_price_predictions',
    'market_updates', 
    'model_performance',
    'user_interactions'
]

print("\n📋 Setting up Kafka topics...")
for topic in topics_to_create:
    success = kafka_streaming.create_topic(topic, num_partitions=3, replication_factor=1)
    if success:
        print(f"✅ Topic '{topic}' ready for streaming")

# ==================== PREDICTION PROCESSING FUNCTIONS ====================
def process_prediction_message(message):
    """Process incoming prediction messages"""
    try:
        if isinstance(message, dict):
            property_id = message.get('property_id', 'Unknown')
            price = message.get('predicted_price', 0)
            confidence = message.get('confidence_score', 0)
            
            print(f"🏡 {property_id}: ${price:,.0f} (confidence: {confidence:.2f})")
    
    except Exception as e:
        print(f"❌ Error processing message: {e}")

def process_market_update(message):
    """Process market update messages"""
    try:
        if isinstance(message, dict):
            trend = message.get('market_trend', 'unknown')
            avg_price = message.get('average_price', 0)
            
            print(f"📈 Market Update: {trend.upper()} trend, avg price: ${avg_price:,.0f}")
    
    except Exception as e:
        print(f"❌ Error processing market update: {e}")

# Subscribe to topics
kafka_streaming.subscribe_to_topic('house_price_predictions', process_prediction_message)
kafka_streaming.subscribe_to_topic('market_updates', process_market_update)

print("\n✅ Kafka streaming system initialized and ready!")
print("💡 Use kafka_streaming.produce_message(topic, data) to send messages")
print("💡 Use kafka_streaming.display_metrics() to view performance")

# ==================== EXAMPLE USAGE ====================
print("\n🧪 TESTING KAFKA STREAMING WITH SAMPLE DATA")
print("-" * 40)

# Test with sample prediction
sample_prediction = {
    'property_id': 'TEST_001',
    'predicted_price': 245000,
    'confidence_score': 0.92,
    'features': {
        'GrLivArea': 1500,
        'OverallQual': 7,
        'YearBuilt': 2010
    },
    'market_segment': 'mid-range'
}

# Send test message
kafka_streaming.produce_message('house_price_predictions', sample_prediction)

# Test market update
sample_market_update = {
    'market_trend': 'bullish',
    'average_price': 280000,
    'volume_change': 0.05,
    'interest_rate': 4.5
}

kafka_streaming.produce_message('market_updates', sample_market_update)

# Display initial metrics
print("\n📊 INITIAL STREAMING METRICS:")
kafka_streaming.display_metrics()

print("\n🎉 KAFKA STREAMING SETUP COMPLETED!")
print("=" * 50)
print("✅ Kafka streaming system is ready for real-time predictions")
print("✅ Topics created and subscribers configured")
print("✅ Test messages sent successfully")
print(f"✅ Streaming mode: {kafka_streaming.get_streaming_metrics()['Mode']}")

print("\n💡 NEXT STEPS:")
print("• Create your ML model in a separate cell")
print("• Use kafka_streaming.produce_message() to send predictions")
print("• Monitor performance with kafka_streaming.display_metrics()")
print("• Scale with multiple consumers for production")

# Store kafka_streaming for use in other cells
print("\n📝 kafka_streaming object is available for use in other cells")
