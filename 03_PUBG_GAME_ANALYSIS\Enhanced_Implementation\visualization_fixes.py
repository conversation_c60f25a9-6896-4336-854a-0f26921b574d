"""
PUBG Game Data Analysis - Visualization Fixes
==============================================

This file contains corrected visualization code to fix the issues in the Jupyter notebook.

IDENTIFIED ISSUES:
1. Outdated Plotly configuration (init_notebook_mode is deprecated)
2. Incorrect renderer settings for Jupyter
3. Missing matplotlib backend configuration
4. No actual matplotlib plotting code despite imports

SOLUTIONS PROVIDED:
1. Modern Plotly configuration for Jupyter
2. Proper matplotlib backend setup
3. Alternative renderer options
4. Test functions to verify visualization setup
"""

# Core Data Science Libraries
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Statistical Analysis
from scipy import stats
from scipy.stats import normaltest, skew, kurtosis

# Static Visualization
import matplotlib.pyplot as plt
import seaborn as sns

# Interactive Visualization
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.io as pio

# System libraries
import os
import time
import datetime

def setup_visualization_environment():
    """
    Setup proper visualization environment for Jupyter notebooks.
    
    This function configures both matplotlib and Plotly for optimal
    display in Jupyter environments.
    """
    print("Setting up visualization environment...")
    
    # Configure matplotlib for Jupyter
    # This magic command ensures plots display inline
    try:
        from IPython import get_ipython
        ipython = get_ipython()
        if ipython is not None:
            ipython.magic('matplotlib inline')
            print("✓ Matplotlib inline mode activated")
    except:
        print("⚠ Could not set matplotlib inline mode (not in Jupyter)")
    
    # Set matplotlib style
    plt.style.use('seaborn-v0_8')
    sns.set_palette("husl")
    print("✓ Matplotlib style configured")
    
    # Configure Plotly for Jupyter
    # Try different renderers based on environment
    renderers_to_try = [
        'notebook_connected',  # Modern Jupyter notebooks
        'iframe',              # Fallback for most environments
        'jupyterlab',          # JupyterLab specific
        'colab',               # Google Colab
        'browser'              # Opens in browser
    ]
    
    for renderer in renderers_to_try:
        try:
            pio.renderers.default = renderer
            print(f"✓ Plotly renderer set to: {renderer}")
            break
        except:
            continue
    else:
        print("⚠ Could not set Plotly renderer")
    
    # Set pandas display options
    pd.set_option('display.max_columns', None)
    pd.set_option('display.precision', 3)
    print("✓ Pandas display options configured")
    
    print("Visualization environment setup complete!")
    return True

def test_matplotlib_plotting():
    """
    Test matplotlib plotting functionality.
    """
    print("Testing matplotlib plotting...")
    
    # Create sample data
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    
    # Create a simple plot
    fig, ax = plt.subplots(figsize=(8, 5))
    ax.plot(x, y, label='sin(x)')
    ax.set_title('Matplotlib Test Plot')
    ax.set_xlabel('X values')
    ax.set_ylabel('Y values')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    print("✓ Matplotlib test completed")
    return fig

def test_plotly_plotting():
    """
    Test Plotly plotting functionality.
    """
    print("Testing Plotly plotting...")
    
    # Create sample data
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    
    # Create interactive plot
    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=x, 
        y=y, 
        mode='lines',
        name='sin(x)',
        line=dict(color='blue', width=2)
    ))
    
    fig.update_layout(
        title='Plotly Test Plot',
        xaxis_title='X values',
        yaxis_title='Y values',
        template='plotly_white',
        width=800,
        height=500
    )
    
    fig.show()
    
    print("✓ Plotly test completed")
    return fig

def create_sample_pubg_visualization(sample_data=None):
    """
    Create a sample PUBG-style visualization to test the setup.
    """
    print("Creating sample PUBG visualization...")
    
    # Create sample data if none provided
    if sample_data is None:
        np.random.seed(42)
        n_samples = 1000
        sample_data = pd.DataFrame({
            'kills': np.random.poisson(2, n_samples),
            'damageDealt': np.random.normal(500, 200, n_samples),
            'walkDistance': np.random.normal(1000, 300, n_samples),
            'winPlacePerc': np.random.beta(2, 5, n_samples)
        })
        # Ensure non-negative values
        sample_data['damageDealt'] = np.maximum(sample_data['damageDealt'], 0)
        sample_data['walkDistance'] = np.maximum(sample_data['walkDistance'], 0)
    
    # Create interactive scatter plot
    fig = go.Figure()
    
    fig.add_trace(go.Scatter(
        x=sample_data['walkDistance'],
        y=sample_data['damageDealt'],
        mode='markers',
        marker=dict(
            size=(sample_data['kills'] + 1) * 8,
            color=sample_data['winPlacePerc'],
            colorscale='Viridis',
            opacity=0.7,
            line=dict(width=1, color='black'),
            colorbar=dict(
                title="Win Placement Percentile",
                titleside="right"
            )
        ),
        hovertemplate='<b>Walk Distance</b>: %{x:.0f}m<br>' +
                     '<b>Damage Dealt</b>: %{y:.0f}<br>' +
                     '<b>Kills</b>: %{customdata[0]}<br>' +
                     '<b>Win Placement</b>: %{customdata[1]:.3f}<extra></extra>',
        customdata=np.column_stack((sample_data['kills'], sample_data['winPlacePerc'])),
        name='Players'
    ))
    
    fig.update_layout(
        title={
            'text': 'Sample PUBG Player Performance Analysis<br><sub>Bubble size = Kills | Color = Win Placement</sub>',
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 16, 'family': 'Arial Black'}
        },
        xaxis_title='Walk Distance (meters)',
        yaxis_title='Damage Dealt',
        width=900,
        height=600,
        template='plotly_white'
    )
    
    fig.show()
    
    print("✓ Sample PUBG visualization created")
    return fig

def diagnose_visualization_issues():
    """
    Diagnose common visualization issues in Jupyter notebooks.
    """
    print("VISUALIZATION DIAGNOSTICS")
    print("=" * 50)
    
    # Check if we're in Jupyter
    try:
        from IPython import get_ipython
        ipython = get_ipython()
        if ipython is not None:
            print("✓ Running in Jupyter environment")
        else:
            print("⚠ Not running in Jupyter environment")
    except ImportError:
        print("⚠ IPython not available")
    
    # Check matplotlib backend
    print(f"Matplotlib backend: {plt.get_backend()}")
    
    # Check Plotly renderer
    print(f"Plotly default renderer: {pio.renderers.default}")
    
    # Check available Plotly renderers
    print("Available Plotly renderers:")
    for renderer in pio.renderers:
        print(f"  - {renderer}")
    
    # Check versions
    print(f"Matplotlib version: {plt.__version__}")
    print(f"Plotly version: {pio.__version__}")
    print(f"Pandas version: {pd.__version__}")
    print(f"NumPy version: {np.__version__}")
    
    return True

if __name__ == "__main__":
    # Run diagnostics and setup
    diagnose_visualization_issues()
    print("\n")
    setup_visualization_environment()
    print("\n")
    
    # Test visualizations
    print("Running visualization tests...")
    test_matplotlib_plotting()
    test_plotly_plotting()
    create_sample_pubg_visualization()
    
    print("\nAll tests completed!")
