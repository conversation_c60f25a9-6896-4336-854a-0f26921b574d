# ===== GRAPH-BASED FEATURE ENGINEERING VISUALIZATION =====
# Extracted from Section 44: Graph Neural Networks
# Run this in a separate cell for graph visualization

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import networkx as nx
from sklearn.neighbors import NearestNeighbors
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_squared_error
import warnings
warnings.filterwarnings('ignore')

print("🧠 GRAPH-BASED FEATURE ENGINEERING VISUALIZATION")
print("=" * 60)

# Check if data is available
if 'df_processed' in globals() and df_processed is not None and 'SalePrice' in df_processed.columns:
    
    # Prepare data for graph construction
    print(f"\n🎯 PREPARING DATA FOR GRAPH CONSTRUCTION:")
    
    # Select key features for graph construction
    graph_features = ['GrLivArea', 'OverallQual', 'YearBuilt', 'TotalBsmtSF', 'GarageCars']
    available_features = [col for col in graph_features if col in df_processed.columns]
    
    if len(available_features) < 3:
        print("❌ Insufficient features for graph construction")
        print("Available features:", available_features)
    else:
        print(f"  • Using {len(available_features)} features: {available_features}")
        
        # Prepare graph data (sample for visualization)
        sample_size = min(200, len(df_processed))  # Use smaller sample for visualization
        sample_indices = np.random.choice(len(df_processed), sample_size, replace=False)
        graph_data = df_processed.iloc[sample_indices][available_features + ['SalePrice']].copy()
        
        # Remove any missing values
        graph_data = graph_data.dropna()
        print(f"  • Graph nodes (houses): {len(graph_data)}")
        
        # Prepare features and target
        X_graph = graph_data[available_features].values
        y_graph = graph_data['SalePrice'].values
        
        # Scale features for similarity calculation
        scaler = StandardScaler()
        X_graph_scaled = scaler.fit_transform(X_graph)
        
        # 1. GRAPH CONSTRUCTION
        print(f"\n🔗 CONSTRUCTING PROPERTY SIMILARITY GRAPH:")
        
        # Build k-nearest neighbors graph
        k_neighbors = 5
        nn_model = NearestNeighbors(n_neighbors=k_neighbors + 1, metric='euclidean')
        nn_model.fit(X_graph_scaled)
        
        # Get neighbors for each house
        distances, indices = nn_model.kneighbors(X_graph_scaled)
        
        # Create edge list with similarity weights
        edge_list = []
        similarity_threshold = 0.7
        
        for i in range(len(graph_data)):
            for j_idx in range(1, k_neighbors + 1):  # Skip self (index 0)
                neighbor_idx = indices[i][j_idx]
                distance = distances[i][j_idx]
                
                # Convert distance to similarity (higher similarity = lower distance)
                similarity = 1 / (1 + distance)
                
                if similarity > similarity_threshold:
                    edge_list.append((i, neighbor_idx, similarity))
        
        print(f"  • K-neighbors: {k_neighbors}")
        print(f"  • Similarity threshold: {similarity_threshold}")
        print(f"  • Total edges created: {len(edge_list)}")
        print(f"  • Average edges per node: {len(edge_list) * 2 / len(graph_data):.1f}")
        
        # 2. NETWORKX GRAPH ANALYSIS
        print(f"\n📊 GRAPH ANALYSIS WITH NETWORKX:")
        
        # Create NetworkX graph
        G = nx.Graph()
        
        # Add nodes with features
        for i in range(len(graph_data)):
            G.add_node(i, 
                      price=y_graph[i],
                      living_area=X_graph[i][0] if len(X_graph[i]) > 0 else 0,
                      quality=X_graph[i][1] if len(X_graph[i]) > 1 else 0)
        
        # Add edges with weights
        for i, j, weight in edge_list:
            G.add_edge(i, j, weight=weight)
        
        # Calculate graph statistics
        print(f"  • Nodes: {G.number_of_nodes()}")
        print(f"  • Edges: {G.number_of_edges()}")
        
        if G.number_of_edges() > 0:
            density = nx.density(G)
            avg_clustering = nx.average_clustering(G, weight='weight')
            components = list(nx.connected_components(G))
            largest_component_size = len(max(components, key=len)) if components else 0
            
            print(f"  • Graph density: {density:.3f}")
            print(f"  • Average clustering coefficient: {avg_clustering:.3f}")
            print(f"  • Connected components: {len(components)}")
            print(f"  • Largest component size: {largest_component_size}")
        
        # 3. GRAPH VISUALIZATION
        print(f"\n🎨 CREATING GRAPH VISUALIZATIONS:")
        
        # Create comprehensive visualization
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # Visualization 1: Network Graph with Price-based Node Colors
        print("  • Creating network graph with price-based coloring...")
        
        if G.number_of_nodes() > 0:
            # Use spring layout for better visualization
            pos = nx.spring_layout(G, k=1, iterations=50, seed=42)
            
            # Node colors based on house prices
            node_colors = [G.nodes[node]['price'] for node in G.nodes()]
            node_sizes = [max(20, G.nodes[node]['living_area'] / 100) for node in G.nodes()]
            
            # Draw the graph
            nx.draw(G, pos, ax=ax1,
                   node_color=node_colors, 
                   node_size=node_sizes,
                   cmap='viridis',
                   with_labels=False,
                   edge_color='gray',
                   alpha=0.7,
                   width=0.5)
            
            ax1.set_title('Property Similarity Network\n(Node Color = Price, Size = Living Area)')
            
            # Add colorbar
            sm = plt.cm.ScalarMappable(cmap='viridis', 
                                     norm=plt.Normalize(vmin=min(node_colors), vmax=max(node_colors)))
            sm.set_array([])
            cbar1 = plt.colorbar(sm, ax=ax1, shrink=0.8)
            cbar1.set_label('House Price ($)')
        
        # Visualization 2: Degree Distribution
        print("  • Creating degree distribution plot...")
        
        if G.number_of_nodes() > 0:
            degrees = [G.degree(node) for node in G.nodes()]
            ax2.hist(degrees, bins=max(1, len(set(degrees))), alpha=0.7, color='skyblue', edgecolor='black')
            ax2.set_title('Node Degree Distribution')
            ax2.set_xlabel('Node Degree (Number of Connections)')
            ax2.set_ylabel('Frequency')
            ax2.grid(True, alpha=0.3)
            
            # Add statistics
            avg_degree = np.mean(degrees)
            ax2.axvline(avg_degree, color='red', linestyle='--', 
                       label=f'Average: {avg_degree:.1f}')
            ax2.legend()
        
        # Visualization 3: Price vs Neighborhood Effect
        print("  • Creating price vs neighborhood effect plot...")
        
        # Calculate neighborhood price averages
        neighborhood_prices = []
        actual_prices = []
        
        for node in G.nodes():
            neighbors = list(G.neighbors(node))
            if neighbors:
                neighbor_prices = [G.nodes[neighbor]['price'] for neighbor in neighbors]
                avg_neighbor_price = np.mean(neighbor_prices)
                neighborhood_prices.append(avg_neighbor_price)
                actual_prices.append(G.nodes[node]['price'])
        
        if neighborhood_prices:
            ax3.scatter(neighborhood_prices, actual_prices, alpha=0.6, color='green')
            ax3.plot([min(neighborhood_prices), max(neighborhood_prices)], 
                    [min(neighborhood_prices), max(neighborhood_prices)], 
                    'r--', label='Perfect Correlation')
            ax3.set_xlabel('Average Neighbor Price ($)')
            ax3.set_ylabel('Actual House Price ($)')
            ax3.set_title('House Price vs Neighborhood Effect')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
            
            # Calculate correlation
            correlation = np.corrcoef(neighborhood_prices, actual_prices)[0, 1]
            ax3.text(0.05, 0.95, f'Correlation: {correlation:.3f}', 
                    transform=ax3.transAxes, bbox=dict(boxstyle="round", facecolor='wheat'))
        
        # Visualization 4: Graph Feature Importance
        print("  • Creating graph feature importance plot...")
        
        # Extract graph features for visualization
        def extract_graph_features_viz(graph, prices):
            """Extract graph-based features for visualization"""
            n_nodes = len(graph.nodes())
            features = np.zeros((n_nodes, 4))  # 4 key features for visualization
            
            for i, node in enumerate(graph.nodes()):
                # 1. Node degree
                degree = graph.degree(node)
                
                # 2. Average neighbor price
                neighbors = list(graph.neighbors(node))
                avg_neighbor_price = np.mean([prices[j] for j in neighbors]) if neighbors else prices[node]
                
                # 3. Price deviation from neighbors
                price_deviation = prices[node] - avg_neighbor_price if neighbors else 0
                
                # 4. Clustering coefficient
                clustering = nx.clustering(graph, node, weight='weight')
                
                features[i] = [degree, avg_neighbor_price, price_deviation, clustering]
            
            return features
        
        if G.number_of_nodes() > 10:  # Need sufficient data for model
            graph_features_matrix = extract_graph_features_viz(G, y_graph[:len(G.nodes())])
            feature_names = ['Degree', 'Avg Neighbor Price', 'Price Deviation', 'Clustering']
            
            # Train a simple model to get feature importance
            if len(graph_features_matrix) > 5:
                try:
                    X_train, X_test, y_train, y_test = train_test_split(
                        graph_features_matrix, y_graph[:len(G.nodes())], 
                        test_size=0.3, random_state=42
                    )
                    
                    model = RandomForestRegressor(n_estimators=50, random_state=42)
                    model.fit(X_train, y_train)
                    
                    importances = model.feature_importances_
                    
                    # Plot feature importance
                    bars = ax4.bar(feature_names, importances, color=['lightcoral', 'lightblue', 'lightgreen', 'lightyellow'])
                    ax4.set_title('Graph Feature Importance')
                    ax4.set_ylabel('Feature Importance')
                    ax4.tick_params(axis='x', rotation=45)
                    
                    # Add value labels on bars
                    for bar, importance in zip(bars, importances):
                        height = bar.get_height()
                        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                                f'{importance:.3f}', ha='center', va='bottom')
                    
                    # Calculate and display R² score
                    y_pred = model.predict(X_test)
                    r2 = r2_score(y_test, y_pred)
                    ax4.text(0.05, 0.95, f'Model R²: {r2:.3f}', 
                            transform=ax4.transAxes, bbox=dict(boxstyle="round", facecolor='lightgray'))
                
                except Exception as e:
                    ax4.text(0.5, 0.5, f'Feature importance\ncalculation failed:\n{str(e)[:50]}...', 
                            ha='center', va='center', transform=ax4.transAxes)
                    ax4.set_title('Graph Feature Importance (Error)')
        else:
            ax4.text(0.5, 0.5, 'Insufficient data\nfor feature importance\nanalysis', 
                    ha='center', va='center', transform=ax4.transAxes)
            ax4.set_title('Graph Feature Importance (Insufficient Data)')
        
        plt.tight_layout()
        plt.show()
        
        # 4. SUMMARY STATISTICS
        print(f"\n📊 GRAPH ANALYSIS SUMMARY:")
        print(f"  • Total properties analyzed: {len(graph_data)}")
        print(f"  • Graph connectivity: {G.number_of_edges()} edges")
        print(f"  • Average connections per property: {G.number_of_edges() * 2 / G.number_of_nodes():.1f}")
        
        if neighborhood_prices:
            print(f"  • Neighborhood price correlation: {correlation:.3f}")
        
        print(f"\n💡 GRAPH INSIGHTS:")
        print("  • Properties are connected based on feature similarity")
        print("  • Node colors represent house prices (darker = higher price)")
        print("  • Node sizes represent living area (larger = bigger house)")
        print("  • Graph features capture neighborhood effects and connectivity")
        
        print(f"\n✅ Graph-based feature engineering visualization complete!")
        print("=" * 60)

else:
    print("❌ Cannot create graph visualization - processed data not available")
    print("Please ensure 'df_processed' is loaded with 'SalePrice' column")
    print("\nTo use this visualization:")
    print("1. Load your processed house price data")
    print("2. Ensure it has columns: GrLivArea, OverallQual, YearBuilt, etc.")
    print("3. Run this cell after data is available")
    print("=" * 60)
