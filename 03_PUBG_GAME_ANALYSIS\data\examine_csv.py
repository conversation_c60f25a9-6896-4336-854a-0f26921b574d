#!/usr/bin/env python3
"""
Quick script to examine the PUBG CSV file structure
"""

import pandas as pd
import os

def examine_pubg_csv():
    """Examine the PUBG CSV file structure and content"""
    
    csv_file = 'pubg.csv'
    
    # Check if file exists
    if not os.path.exists(csv_file):
        print(f"Error: {csv_file} not found!")
        return
    
    # Get file size
    file_size = os.path.getsize(csv_file)
    print(f"File size: {file_size:,} bytes ({file_size/1024**2:.2f} MB)")
    
    try:
        # Read just the first few rows to examine structure
        print("\nReading first 5 rows...")
        df_sample = pd.read_csv(csv_file, nrows=5)
        
        print(f"\nDataset shape (sample): {df_sample.shape}")
        
        print(f"\nColumns ({len(df_sample.columns)}):")
        for i, col in enumerate(df_sample.columns, 1):
            print(f"  {i:2d}. {col}")
        
        print(f"\nData types:")
        print(df_sample.dtypes)
        
        print(f"\nFirst 3 rows:")
        print(df_sample.head(3))
        
        print(f"\nBasic statistics:")
        print(df_sample.describe())
        
        # Try to get total row count
        print(f"\nCounting total rows...")
        total_rows = sum(1 for line in open(csv_file)) - 1  # -1 for header
        print(f"Total rows: {total_rows:,}")
        
    except Exception as e:
        print(f"Error reading CSV: {e}")

if __name__ == "__main__":
    examine_pubg_csv()
