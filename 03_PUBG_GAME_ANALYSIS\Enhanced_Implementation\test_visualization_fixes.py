#!/usr/bin/env python3
"""
Test script for PUBG visualization fixes
This script tests all the functionality from the notebook
"""

import sys
import os
import traceback

def test_imports():
    """Test all required imports"""
    print("🔍 Testing imports...")
    
    try:
        import pandas as pd
        import numpy as np
        import matplotlib.pyplot as plt
        import seaborn as sns
        import plotly.express as px
        import plotly.graph_objects as go
        import plotly.io as pio
        from plotly.subplots import make_subplots
        import gc
        
        print("✅ All imports successful")
        return True, {
            'pandas': pd.__version__,
            'numpy': np.__version__,
            'matplotlib': plt.matplotlib.__version__,
            'plotly': pio.__version__
        }
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False, {}

def test_visualization_setup():
    """Test visualization environment setup"""
    print("\n🔧 Testing visualization setup...")
    
    try:
        import matplotlib.pyplot as plt
        import plotly.io as pio
        
        # Test matplotlib backend
        backend = plt.get_backend()
        print(f"✅ Matplotlib backend: {backend}")
        
        # Test plotly renderers
        available_renderers = list(pio.renderers)
        print(f"✅ Available Plotly renderers: {len(available_renderers)}")
        
        # Try setting a renderer
        for renderer in ['notebook_connected', 'iframe', 'browser']:
            try:
                pio.renderers.default = renderer
                print(f"✅ Plotly renderer set to: {renderer}")
                break
            except:
                continue
        
        return True
    except Exception as e:
        print(f"❌ Visualization setup error: {e}")
        return False

def test_pubg_data_loading():
    """Test PUBG data loading"""
    print("\n📊 Testing PUBG data loading...")
    
    DATA_PATH = '../data/pubg.csv'
    
    # Check file existence
    if os.path.exists(DATA_PATH):
        file_size = os.path.getsize(DATA_PATH)
        print(f"✅ PUBG file found: {file_size:,} bytes ({file_size/1024**2:.2f} MB)")
        
        try:
            import pandas as pd
            
            # Try to read just the header
            df_peek = pd.read_csv(DATA_PATH, nrows=0)
            columns = df_peek.columns.tolist()
            print(f"✅ File readable, {len(columns)} columns found")
            print(f"📋 Columns: {columns[:10]}{'...' if len(columns) > 10 else ''}")
            
            # Try to read a small sample
            df_sample = pd.read_csv(DATA_PATH, nrows=100)
            print(f"✅ Sample loaded: {df_sample.shape}")
            
            # Check for key PUBG columns
            key_columns = ['kills', 'damageDealt', 'walkDistance', 'winPlacePerc']
            found_columns = [col for col in key_columns if col in df_sample.columns]
            print(f"🎮 Key PUBG columns found: {found_columns}")
            
            return True, df_sample
            
        except Exception as e:
            print(f"❌ Error reading PUBG data: {e}")
            return False, None
    else:
        print(f"⚠️ PUBG file not found: {DATA_PATH}")
        print("📝 Creating synthetic data for testing...")
        
        # Create synthetic PUBG data
        import pandas as pd
        import numpy as np
        
        np.random.seed(42)
        n_samples = 1000
        
        synthetic_data = pd.DataFrame({
            'kills': np.random.poisson(2, n_samples),
            'damageDealt': np.random.normal(500, 200, n_samples),
            'walkDistance': np.random.normal(1000, 300, n_samples),
            'winPlacePerc': np.random.beta(2, 5, n_samples)
        })
        
        # Clean data
        synthetic_data['damageDealt'] = np.maximum(synthetic_data['damageDealt'], 0)
        synthetic_data['walkDistance'] = np.maximum(synthetic_data['walkDistance'], 0)
        
        print(f"✅ Synthetic PUBG data created: {synthetic_data.shape}")
        return True, synthetic_data

def test_matplotlib_plotting(data):
    """Test matplotlib plotting"""
    print("\n📈 Testing matplotlib plotting...")
    
    try:
        import matplotlib.pyplot as plt
        import numpy as np
        
        if data is not None and 'kills' in data.columns:
            # Test with real data
            fig, ax = plt.subplots(figsize=(8, 5))
            ax.hist(data['kills'], bins=20, alpha=0.7, edgecolor='black')
            ax.set_title('PUBG Kills Distribution (Test)')
            ax.set_xlabel('Number of Kills')
            ax.set_ylabel('Frequency')
            ax.grid(True, alpha=0.3)
            
            # Save instead of show for testing
            plt.savefig('test_matplotlib_plot.png', dpi=100, bbox_inches='tight')
            plt.close()
            
            print("✅ Matplotlib plot created and saved as 'test_matplotlib_plot.png'")
        else:
            # Test with simple data
            x = np.linspace(0, 10, 100)
            y = np.sin(x)
            
            fig, ax = plt.subplots(figsize=(8, 5))
            ax.plot(x, y, label='sin(x)')
            ax.set_title('Matplotlib Test Plot')
            ax.legend()
            
            plt.savefig('test_matplotlib_simple.png', dpi=100, bbox_inches='tight')
            plt.close()
            
            print("✅ Simple matplotlib plot created and saved as 'test_matplotlib_simple.png'")
        
        return True
    except Exception as e:
        print(f"❌ Matplotlib plotting error: {e}")
        traceback.print_exc()
        return False

def test_plotly_plotting(data):
    """Test plotly plotting"""
    print("\n🎨 Testing Plotly plotting...")
    
    try:
        import plotly.graph_objects as go
        import plotly.io as pio
        
        if data is not None and 'kills' in data.columns and 'damageDealt' in data.columns:
            # Test with real PUBG data
            fig = go.Figure()
            
            fig.add_trace(go.Scatter(
                x=data['walkDistance'] if 'walkDistance' in data.columns else data.index,
                y=data['damageDealt'],
                mode='markers',
                marker=dict(
                    size=(data['kills'] + 1) * 6,
                    color=data['winPlacePerc'] if 'winPlacePerc' in data.columns else data['kills'],
                    colorscale='Viridis',
                    opacity=0.6
                ),
                name='PUBG Players'
            ))
            
            fig.update_layout(
                title='PUBG Player Performance (Test)',
                xaxis_title='Walk Distance' if 'walkDistance' in data.columns else 'Index',
                yaxis_title='Damage Dealt',
                template='plotly_white'
            )
            
            # Save as HTML
            fig.write_html('test_plotly_plot.html')
            print("✅ Plotly plot created and saved as 'test_plotly_plot.html'")
            
        else:
            # Test with simple data
            import numpy as np
            x = np.linspace(0, 10, 50)
            y = np.sin(x)
            
            fig = go.Figure()
            fig.add_trace(go.Scatter(x=x, y=y, mode='lines', name='sin(x)'))
            fig.update_layout(title='Plotly Test Plot', template='plotly_white')
            
            fig.write_html('test_plotly_simple.html')
            print("✅ Simple Plotly plot created and saved as 'test_plotly_simple.html'")
        
        return True
    except Exception as e:
        print(f"❌ Plotly plotting error: {e}")
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🎮 PUBG VISUALIZATION FIXES - TEST SUITE")
    print("=" * 60)
    
    # Test 1: Imports
    imports_ok, versions = test_imports()
    if not imports_ok:
        print("❌ Cannot proceed - import errors")
        return False
    
    print(f"\n📦 Package versions:")
    for package, version in versions.items():
        print(f"  {package}: {version}")
    
    # Test 2: Visualization setup
    setup_ok = test_visualization_setup()
    if not setup_ok:
        print("⚠️ Visualization setup issues detected")
    
    # Test 3: Data loading
    data_ok, data = test_pubg_data_loading()
    if not data_ok:
        print("❌ Cannot proceed - data loading failed")
        return False
    
    # Test 4: Matplotlib
    matplotlib_ok = test_matplotlib_plotting(data)
    
    # Test 5: Plotly
    plotly_ok = test_plotly_plotting(data)
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Imports: {'PASS' if imports_ok else 'FAIL'}")
    print(f"✅ Visualization Setup: {'PASS' if setup_ok else 'FAIL'}")
    print(f"✅ Data Loading: {'PASS' if data_ok else 'FAIL'}")
    print(f"✅ Matplotlib: {'PASS' if matplotlib_ok else 'FAIL'}")
    print(f"✅ Plotly: {'PASS' if plotly_ok else 'FAIL'}")
    
    all_tests_passed = all([imports_ok, setup_ok, data_ok, matplotlib_ok, plotly_ok])
    
    if all_tests_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Your visualization setup is working correctly")
        print("✅ You can now run the Jupyter notebook with confidence")
        
        print("\n📁 Generated test files:")
        if os.path.exists('test_matplotlib_plot.png'):
            print("  📈 test_matplotlib_plot.png")
        if os.path.exists('test_matplotlib_simple.png'):
            print("  📈 test_matplotlib_simple.png")
        if os.path.exists('test_plotly_plot.html'):
            print("  🎨 test_plotly_plot.html")
        if os.path.exists('test_plotly_simple.html'):
            print("  🎨 test_plotly_simple.html")
            
    else:
        print("\n⚠️ SOME TESTS FAILED")
        print("Please check the error messages above and fix the issues")
    
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
