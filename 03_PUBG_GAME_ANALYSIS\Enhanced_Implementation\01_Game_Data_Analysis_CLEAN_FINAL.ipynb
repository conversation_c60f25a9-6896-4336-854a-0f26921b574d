# Core Data Science Libraries
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Statistical Analysis
from scipy import stats
from scipy.stats import normaltest, skew, kurtosis

# Static Visualization
import matplotlib.pyplot as plt
import seaborn as sns

# Interactive Visualization
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.io as pio

# System libraries
import os
import time
import datetime

# Visualization configuration
# Configure matplotlib for Jupyter
%matplotlib inline
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Configure Plotly for Jupyter
# Modern Plotly configuration for Jupyter notebooks
pio.renderers.default = 'notebook_connected'
# Alternative renderers if the above doesn't work:
# pio.renderers.default = 'iframe'
# pio.renderers.default = 'colab' # for Google Colab
# pio.renderers.default = 'jupyterlab' # for JupyterLab
pd.set_option('display.max_columns', None)
pd.set_option('display.precision', 3)

print("[SUCCESS] All libraries loaded successfully")
print("PUBG GAME ANALYTICS - INTERACTIVE EDA SYSTEM")
print("=" * 60)
print(f"Analysis Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print(f"Environment: Professional Data Science Pipeline")
print(f"Objective: Interactive Gaming Data Exploration")
print("=" * 60)

# Define data path
DATA_PATH = '../data/pubg.csv'

# Validate file existence
if os.path.exists(DATA_PATH):
    file_size = os.path.getsize(DATA_PATH)
    print(f"[SUCCESS] File found: {file_size:,} bytes ({file_size/1024**2:.2f} MB)")
else:
    print(f"[ERROR] File not found: {DATA_PATH}")

# Load dataset with optimization
start_time = time.time()

df_pubg = pd.read_csv(DATA_PATH, 
                     low_memory=False,
                     dtype={
                         'Id': 'string',
                         'groupId': 'string', 
                         'matchId': 'string',
                         'matchType': 'category'
                     })

load_time = time.time() - start_time

print(f"[SUCCESS] Data loaded in {load_time:.2f} seconds")
print(f"[INFO] Dataset shape: {df_pubg.shape[0]:,} rows x {df_pubg.shape[1]} columns")

# Comprehensive dataset overview
print("COMPREHENSIVE DATASET OVERVIEW")
print("=" * 50)

# Basic dataset characteristics
print("\n[BASIC CHARACTERISTICS]")
print(f"   Total Records: {len(df_pubg):,}")
print(f"   Total Features: {len(df_pubg.columns)}")
print(f"   Dataset Shape: {df_pubg.shape[0]:,} rows × {df_pubg.shape[1]} columns")
print(f"   Memory Usage: {df_pubg.memory_usage(deep=True).sum() / 1024**2:.2f} MB")

# Data quality assessment
print("\n[DATA QUALITY ASSESSMENT]")
missing_values = df_pubg.isnull().sum().sum()
total_cells = len(df_pubg) * len(df_pubg.columns)
data_completeness = ((total_cells - missing_values) / total_cells) * 100

print(f"   Missing Values: {missing_values:,}")
print(f"   Data Completeness: {data_completeness:.2f}%")
print(f"   Duplicate Rows: {df_pubg.duplicated().sum():,}")

if missing_values == 0:
    print("   [SUCCESS] Perfect data quality - No missing values detected")

# Feature type classification
numerical_features = df_pubg.select_dtypes(include=[np.number]).columns.tolist()
categorical_features = df_pubg.select_dtypes(include=['object', 'category']).columns.tolist()
identifier_features = [col for col in df_pubg.columns if 'id' in col.lower() or 'Id' in col]
analysis_features = [col for col in numerical_features if 'id' not in col.lower()]

print("\n[FEATURE TYPE CLASSIFICATION]")
print(f"   Numerical Features: {len(numerical_features)}")
print(f"   Categorical Features: {len(categorical_features)}")
print(f"   Identifier Features: {len(identifier_features)}")
print(f"   Analysis Features: {len(analysis_features)} (numerical excluding IDs)")

print("\n[SUCCESS] Dataset analysis completed")

# Gaming statistics overview
print("\n[GAMING STATISTICS OVERVIEW]")

# Key gaming metrics
if 'kills' in df_pubg.columns:
    print(f"   Average Kills per Match: {df_pubg['kills'].mean():.2f}")
    print(f"   Maximum Kills in Single Match: {df_pubg['kills'].max()}")
    print(f"   Players with 0 Kills: {(df_pubg['kills'] == 0).sum():,} ({(df_pubg['kills'] == 0).mean()*100:.1f}%)")
    print(f"   Players with 5+ Kills: {(df_pubg['kills'] >= 5).sum():,} ({(df_pubg['kills'] >= 5).mean()*100:.1f}%)")
    print(f"   Players with 10+ Kills: {(df_pubg['kills'] >= 10).sum():,} ({(df_pubg['kills'] >= 10).mean()*100:.1f}%)")

if 'winPlacePerc' in df_pubg.columns:
    print(f"   Average Win Placement: {df_pubg['winPlacePerc'].mean():.3f}")
    print(f"   Top 10% Players: {(df_pubg['winPlacePerc'] >= 0.9).sum():,} ({(df_pubg['winPlacePerc'] >= 0.9).mean()*100:.1f}%)")
    print(f"   Top 25% Players: {(df_pubg['winPlacePerc'] >= 0.75).sum():,} ({(df_pubg['winPlacePerc'] >= 0.75).mean()*100:.1f}%)")

if 'damageDealt' in df_pubg.columns:
    print(f"   Average Damage per Match: {df_pubg['damageDealt'].mean():.0f}")
    print(f"   Maximum Damage in Single Match: {df_pubg['damageDealt'].max():.0f}")

# Match type distribution
if 'matchType' in df_pubg.columns:
    print("\n[MATCH TYPE DISTRIBUTION]")
    match_distribution = df_pubg['matchType'].value_counts()
    total_matches = len(df_pubg)
    
    for match_type, count in match_distribution.items():
        percentage = (count / total_matches) * 100
        print(f"   {match_type}: {count:,} matches ({percentage:.1f}%)")

def create_interactive_count_plots(df, columns, title_prefix):
    """
    Create interactive count plots for discrete features.
    
    Features:
    - Interactive Plotly visualizations
    - Multi-panel subplot layouts
    - Professional hover information
    """
    
    # Verify columns exist in dataframe
    missing_cols = [col for col in columns if col not in df.columns]
    if missing_cols:
        print(f"[ERROR] Missing columns in dataset: {missing_cols}")
        return None
    
    # Calculate subplot layout
    n_cols = len(columns)
    rows = (n_cols + 2) // 3  # 3 columns per row
    cols = min(3, n_cols)
    
    # Create subplots
    fig = make_subplots(
        rows=rows, cols=cols,
        subplot_titles=[f'{col.title()} Distribution' for col in columns],
        vertical_spacing=0.12,
        horizontal_spacing=0.1
    )
    
    # Add count plots for each column
    for i, col in enumerate(columns):
        row = (i // 3) + 1
        col_pos = (i % 3) + 1
        
        # Get value counts (limit to top 15 for readability)
        value_counts = df[col].value_counts().sort_index().head(15)
        
        # Add bar trace
        fig.add_trace(
            go.Bar(
                x=value_counts.index,
                y=value_counts.values,
                name=col,
                marker_color=px.colors.qualitative.Set3[i % len(px.colors.qualitative.Set3)],
                hovertemplate=f'<b>{col.title()}</b>: %{{x}}<br>' +
                             '<b>Count</b>: %{{y:,}}<br>' +
                             '<b>Percentage</b>: %{{customdata:.1f}}%<extra></extra>',
                customdata=(value_counts.values / len(df) * 100),
                showlegend=False
            ),
            row=row, col=col_pos
        )
    
    # Update layout
    fig.update_layout(
        title={
            'text': f'{title_prefix} - Interactive Distribution Analysis',
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 18, 'family': 'Arial Black'}
        },
        height=300 * rows,
        template='plotly_white'
    )
    
    return fig

print("[SUCCESS] Interactive count plot function defined")

# Define combat features
discrete_cols_combat = ['kills', 'assists', 'DBNOs', 'headshotKills', 'teamKills', 'roadKills']

print("Combat Features Analysis")
print(f"Features: {discrete_cols_combat}")

# Create interactive count plots for combat features
combat_fig = create_interactive_count_plots(df_pubg, discrete_cols_combat, "Combat Features")

if combat_fig is not None:
    combat_fig.show()
    print("[SUCCESS] Combat features visualization displayed")
else:
    print("[ERROR] Failed to create combat features visualization")

# Define item features
discrete_cols_items = ['heals', 'boosts', 'weaponsAcquired', 'vehicleDestroys']

print("Item Features Analysis")
print(f"Features: {discrete_cols_items}")

# Create interactive count plots for item features
items_fig = create_interactive_count_plots(df_pubg, discrete_cols_items, "Item Usage Features")

if items_fig is not None:
    items_fig.show()
    print("[SUCCESS] Item features visualization displayed")
else:
    print("[ERROR] Failed to create item features visualization")

def create_interactive_correlation_heatmap(df, sample_size=10000):
    """
    Create interactive correlation heatmap.
    
    Features:
    - Interactive correlation analysis
    - Performance optimization with sampling
    - Professional heatmap styling
    """
    
    print(f"Computing correlations for sample of {sample_size:,} records...")
    
    # Select numerical features (exclude identifiers)
    numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()
    analysis_cols = [col for col in numerical_cols if 'id' not in col.lower()]
    
    # Sample data for performance
    sample_df = df[analysis_cols].sample(n=min(sample_size, len(df)), random_state=42)
    
    # Compute correlation matrix
    corr_matrix = sample_df.corr()
    
    # Create interactive heatmap
    fig = go.Figure(data=go.Heatmap(
        z=corr_matrix.values,
        x=corr_matrix.columns,
        y=corr_matrix.columns,
        colorscale='RdBu',
        zmid=0,
        hovertemplate='<b>%{x}</b> vs <b>%{y}</b><br>' +
                     'Correlation: %{z:.3f}<extra></extra>',
        colorbar=dict(
            title="Correlation Coefficient",
            titleside="right"
        )
    ))
    
    # Update layout
    fig.update_layout(
        title={
            'text': 'Interactive Correlation Matrix - PUBG Features',
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 18, 'family': 'Arial Black'}
        },
        width=800,
        height=800,
        template='plotly_white'
    )
    
    print(f"Correlation heatmap created for {len(analysis_cols)} features")
    
    return fig

# Generate and display correlation heatmap
print("Correlation Heatmap Analysis")
correlation_fig = create_interactive_correlation_heatmap(df_pubg)

if correlation_fig is not None:
    correlation_fig.show()
    print("[SUCCESS] Correlation heatmap visualization displayed")
else:
    print("[ERROR] Failed to create correlation heatmap visualization")

def create_advanced_scatter_plot(df, sample_size=5000):
    """
    Create advanced multi-dimensional scatter plot with bubble sizing and color mapping.
    
    Features:
    - Multi-dimensional data visualization
    - Interactive bubble charts
    - Advanced color mapping and sizing
    """
    
    print(f"Creating advanced scatter plot with {sample_size:,} samples...")
    
    # Sample data for performance
    sample_df = df.sample(n=min(sample_size, len(df)), random_state=42)
    
    # Create scatter plot with multiple dimensions
    fig = go.Figure()
    
    # Add scatter trace
    fig.add_trace(go.Scatter(
        x=sample_df['walkDistance'],
        y=sample_df['damageDealt'],
        mode='markers',
        marker=dict(
            size=(sample_df['kills'] + 1) * 8,  # Size based on kills
            color=sample_df['winPlacePerc'],    # Color based on win placement
            colorscale='Viridis',
            opacity=0.7,
            line=dict(width=1, color='black'),
            colorbar=dict(
                title="Win Placement Percentile",
                titleside="right"
            )
        ),
        hovertemplate='<b>Walk Distance</b>: %{x:.0f}m<br>' +
                     '<b>Damage Dealt</b>: %{y:.0f}<br>' +
                     '<b>Kills</b>: %{customdata[0]}<br>' +
                     '<b>Win Placement</b>: %{customdata[1]:.3f}<extra></extra>',
        customdata=np.column_stack((sample_df['kills'], sample_df['winPlacePerc'])),
        name='Players'
    ))
    
    # Update layout
    fig.update_layout(
        title={
            'text': 'Multi-Dimensional Player Performance Analysis<br><sub>Bubble size = Kills | Color = Win Placement</sub>',
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 16, 'family': 'Arial Black'}
        },
        xaxis_title='Walk Distance (meters)',
        yaxis_title='Damage Dealt',
        width=900,
        height=600,
        template='plotly_white'
    )
    
    print("Advanced scatter plot created")
    
    return fig

# Generate and display advanced scatter plot
print("Advanced Scatter Plot Analysis")
scatter_fig = create_advanced_scatter_plot(df_pubg)

if scatter_fig is not None:
    scatter_fig.show()
    print("[SUCCESS] Advanced scatter plot visualization displayed")
else:
    print("[ERROR] Failed to create scatter plot visualization")

def create_interactive_distribution_plots(df, continuous_features, sample_size=10000):
    """
    Create interactive distribution plots with histograms.
    
    Features:
    - Interactive distribution analysis
    - Multi-panel subplot layouts
    - Professional styling
    """
    
    print(f"Creating distribution plots for {len(continuous_features)} features...")
    
    # Sample data for performance
    sample_df = df[continuous_features].sample(n=min(sample_size, len(df)), random_state=42)
    
    # Calculate subplot layout
    n_features = len(continuous_features)
    rows = (n_features + 2) // 3  # 3 columns per row
    cols = min(3, n_features)
    
    # Create subplots
    fig = make_subplots(
        rows=rows, cols=cols,
        subplot_titles=[f'{feature.title()} Distribution' for feature in continuous_features],
        vertical_spacing=0.12,
        horizontal_spacing=0.1
    )
    
    # Add distribution plots for each feature
    for i, feature in enumerate(continuous_features):
        row = (i // 3) + 1
        col_pos = (i % 3) + 1
        
        # Get feature data
        feature_data = sample_df[feature].dropna()
        
        # Add histogram
        fig.add_trace(
            go.Histogram(
                x=feature_data,
                nbinsx=50,
                name=f'{feature} Histogram',
                opacity=0.7,
                marker_color=px.colors.qualitative.Set2[i % len(px.colors.qualitative.Set2)],
                hovertemplate=f'<b>{feature.title()}</b><br>' +
                             'Range: %{x}<br>' +
                             'Count: %{y}<extra></extra>',
                showlegend=False
            ),
            row=row, col=col_pos
        )
    
    # Update layout
    fig.update_layout(
        title={
            'text': 'Interactive Feature Distribution Analysis',
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 18, 'family': 'Arial Black'}
        },
        height=300 * rows,
        template='plotly_white'
    )
    
    print(f"Distribution plots created for {len(continuous_features)} features")
    
    return fig

# Define continuous features
continuous_features = ['walkDistance', 'rideDistance', 'swimDistance', 'damageDealt', 
                      'longestKill', 'matchDuration']

print("Distribution Analysis")
print(f"Features: {continuous_features}")

# Generate distribution plots
distribution_fig = create_interactive_distribution_plots(df_pubg, continuous_features)

if distribution_fig is not None:
    distribution_fig.show()
    print("[SUCCESS] Distribution analysis visualization displayed")
else:
    print("[ERROR] Failed to create distribution analysis visualization")

def categorize_performance(win_place_perc):
    """
    Categorize player performance based on win placement percentile.
    """
    if win_place_perc >= 0.9:
        return 'Elite (Top 10%)'
    elif win_place_perc >= 0.7:
        return 'Advanced (Top 30%)'
    elif win_place_perc >= 0.5:
        return 'Intermediate (Top 50%)'
    else:
        return 'Beginner (Bottom 50%)'

# Apply performance categorization
df_pubg['performance_tier'] = df_pubg['winPlacePerc'].apply(categorize_performance)

# Analyze performance distribution
performance_distribution = df_pubg['performance_tier'].value_counts()

print("PLAYER PERFORMANCE SEGMENTATION")
print("-" * 40)

for tier, count in performance_distribution.items():
    percentage = (count / len(df_pubg)) * 100
    print(f"   {tier}: {count:,} players ({percentage:.1f}%)")

# Create performance tier visualization
def create_performance_tier_analysis(df):
    """
    Create interactive analysis of performance tiers.
    """
    
    # Create pie chart for performance distribution
    performance_counts = df['performance_tier'].value_counts()
    
    fig = go.Figure(data=[go.Pie(
        labels=performance_counts.index,
        values=performance_counts.values,
        hole=0.4,
        hovertemplate='<b>%{label}</b><br>' +
                     'Players: %{value:,}<br>' +
                     'Percentage: %{percent}<extra></extra>',
        marker_colors=px.colors.qualitative.Set3
    )])
    
    fig.update_layout(
        title={
            'text': 'Player Performance Tier Distribution',
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 18, 'family': 'Arial Black'}
        },
        width=600,
        height=500,
        template='plotly_white'
    )
    
    return fig

# Generate and display performance tier analysis
performance_fig = create_performance_tier_analysis(df_pubg)
performance_fig.show()

print("[SUCCESS] Performance tier analysis visualization displayed")

# Calculate key gaming statistics
print("\nFINAL ANALYSIS SUMMARY")
print("=" * 50)

# Performance insights
elite_players = (df_pubg['performance_tier'] == 'Elite (Top 10%)').sum()
elite_percentage = (elite_players / len(df_pubg)) * 100
print(f"Elite Players: {elite_players:,} ({elite_percentage:.1f}%)")

# Combat statistics
avg_kills = df_pubg['kills'].mean()
avg_damage = df_pubg['damageDealt'].mean()
print(f"Average Kills per Match: {avg_kills:.2f}")
print(f"Average Damage per Match: {avg_damage:.0f}")

# Movement statistics
if 'walkDistance' in df_pubg.columns:
    avg_walk = df_pubg['walkDistance'].mean()
    print(f"Average Walk Distance: {avg_walk:.0f} meters")

# Survival statistics
if 'heals' in df_pubg.columns:
    avg_heals = df_pubg['heals'].mean()
    print(f"Average Heals Used: {avg_heals:.2f}")

print("\n[SUCCESS] PUBG Game Data Analysis Completed")
print(f"[INFO] Analyzed {len(df_pubg):,} player records")
print(f"[INFO] Generated {len(analysis_features)} feature visualizations")
print("[INFO] All interactive visualizations are ready for exploration")