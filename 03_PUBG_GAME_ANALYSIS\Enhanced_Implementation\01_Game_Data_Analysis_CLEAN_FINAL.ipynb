{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PRCP-1012-GameWinnerPred (PUBG Game Data Analysis - EDA)\n", "\n", "## Project Overview\n", "This notebook implements **Phase 1** of the PUBG Game Analytics System, focusing on comprehensive exploratory data analysis using advanced statistical techniques and visualizations.\n", "\n", "## Business Context\n", "PlayerUnknown's Battlegrounds (PUBG) is a battle royale game where 100 players compete for survival. Understanding player behavior, performance patterns, and game dynamics is crucial for:\n", "- **Player Development**: Performance improvement insights\n", "- **Competitive Analysis**: Winning strategy identification\n", "- **Game Balance**: Data-driven optimization\n", "- **Esports Intelligence**: Professional team analytics\n", "\n", "## Technical Features\n", "- **Modular Code Structure**: Small, understandable code blocks\n", "- **Interactive Visualizations**: Professional Plotly dashboards\n", "- **Advanced Analytics**: Statistical analysis and correlation\n", "- **Gaming Domain Expertise**: PUBG-specific insights\n", "\n", "## Dataset Specifications\n", "- **Records**: 100,001 player match performances\n", "- **Features**: 29 comprehensive gaming metrics\n", "- **Size**: 629 MB of structured gaming data\n", "- **Scope**: Multi-modal gameplay analysis (combat, movement, survival, strategy)\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Library Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Core Data Science Libraries\n", "import pandas as pd\n", "import numpy as np\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Statistical Analysis\n", "from scipy import stats\n", "from scipy.stats import normaltest, skew, kurtosis\n", "\n", "# Static Visualization\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# Interactive Visualization\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import plotly.io as pio\n", "\n", "# System libraries\n", "import os\n", "import time\n", "import datetime\n", "\n", "# Visualization configuration\n", "# Configure matplotlib for <PERSON><PERSON><PERSON>\n", "%matplotlib inline\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "# Configure Plotly for <PERSON><PERSON><PERSON>\n", "# Modern Plotly configuration for Jupy<PERSON> notebooks\n", "pio.renderers.default = 'notebook_connected'\n", "# Alternative renderers if the above doesn't work:\n", "# pio.renderers.default = 'iframe'\n", "# pio.renderers.default = 'colab' # for Google Colab\n", "# pio.renderers.default = 'jupyterlab' # for JupyterLab\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.precision', 3)\n", "\n", "print(\"[SUCCESS] All libraries loaded successfully\")\n", "print(\"PUBG GAME ANALYTICS - INTERACTIVE EDA SYSTEM\")\n", "print(\"=\" * 60)\n", "print(f\"Analysis Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(f\"Environment: Professional Data Science Pipeline\")\n", "print(f\"Objective: Interactive Gaming Data Exploration\")\n", "print(\"=\" * 60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Loading and Overview"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define data path\n", "DATA_PATH = '../data/pubg.csv'\n", "\n", "# Validate file existence\n", "if os.path.exists(DATA_PATH):\n", "    file_size = os.path.getsize(DATA_PATH)\n", "    print(f\"[SUCCESS] File found: {file_size:,} bytes ({file_size/1024**2:.2f} MB)\")\n", "else:\n", "    print(f\"[ERROR] File not found: {DATA_PATH}\")\n", "\n", "# Load dataset with optimization\n", "start_time = time.time()\n", "\n", "df_pubg = pd.read_csv(DATA_PATH, \n", "                     low_memory=False,\n", "                     dtype={\n", "                         'Id': 'string',\n", "                         'groupId': 'string', \n", "                         'matchId': 'string',\n", "                         'matchType': 'category'\n", "                     })\n", "\n", "load_time = time.time() - start_time\n", "\n", "print(f\"[SUCCESS] Data loaded in {load_time:.2f} seconds\")\n", "print(f\"[INFO] Dataset shape: {df_pubg.shape[0]:,} rows x {df_pubg.shape[1]} columns\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Dataset Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Comprehensive dataset overview\n", "print(\"COMPREHENSIVE DATASET OVERVIEW\")\n", "print(\"=\" * 50)\n", "\n", "# Basic dataset characteristics\n", "print(\"\\n[BASIC CHARACTERISTICS]\")\n", "print(f\"   Total Records: {len(df_pubg):,}\")\n", "print(f\"   Total Features: {len(df_pubg.columns)}\")\n", "print(f\"   Dataset Shape: {df_pubg.shape[0]:,} rows × {df_pubg.shape[1]} columns\")\n", "print(f\"   Memory Usage: {df_pubg.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "\n", "# Data quality assessment\n", "print(\"\\n[DATA QUALITY ASSESSMENT]\")\n", "missing_values = df_pubg.isnull().sum().sum()\n", "total_cells = len(df_pubg) * len(df_pubg.columns)\n", "data_completeness = ((total_cells - missing_values) / total_cells) * 100\n", "\n", "print(f\"   Missing Values: {missing_values:,}\")\n", "print(f\"   Data Completeness: {data_completeness:.2f}%\")\n", "print(f\"   Duplicate Rows: {df_pubg.duplicated().sum():,}\")\n", "\n", "if missing_values == 0:\n", "    print(\"   [SUCCESS] Perfect data quality - No missing values detected\")\n", "\n", "# Feature type classification\n", "numerical_features = df_pubg.select_dtypes(include=[np.number]).columns.tolist()\n", "categorical_features = df_pubg.select_dtypes(include=['object', 'category']).columns.tolist()\n", "identifier_features = [col for col in df_pubg.columns if 'id' in col.lower() or 'Id' in col]\n", "analysis_features = [col for col in numerical_features if 'id' not in col.lower()]\n", "\n", "print(\"\\n[FEATURE TYPE CLASSIFICATION]\")\n", "print(f\"   Numerical Features: {len(numerical_features)}\")\n", "print(f\"   Categorical Features: {len(categorical_features)}\")\n", "print(f\"   Identifier Features: {len(identifier_features)}\")\n", "print(f\"   Analysis Features: {len(analysis_features)} (numerical excluding IDs)\")\n", "\n", "print(\"\\n[SUCCESS] Dataset analysis completed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Gaming Statistics Overview"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Gaming statistics overview\n", "print(\"\\n[GAMING STATISTICS OVERVIEW]\")\n", "\n", "# Key gaming metrics\n", "if 'kills' in df_pubg.columns:\n", "    print(f\"   Average Kills per Match: {df_pubg['kills'].mean():.2f}\")\n", "    print(f\"   Maximum Kills in Single Match: {df_pubg['kills'].max()}\")\n", "    print(f\"   Players with 0 Kills: {(df_pubg['kills'] == 0).sum():,} ({(df_pubg['kills'] == 0).mean()*100:.1f}%)\")\n", "    print(f\"   Players with 5+ Kills: {(df_pubg['kills'] >= 5).sum():,} ({(df_pubg['kills'] >= 5).mean()*100:.1f}%)\")\n", "    print(f\"   Players with 10+ Kills: {(df_pubg['kills'] >= 10).sum():,} ({(df_pubg['kills'] >= 10).mean()*100:.1f}%)\")\n", "\n", "if 'winPlacePerc' in df_pubg.columns:\n", "    print(f\"   Average Win Placement: {df_pubg['winPlacePerc'].mean():.3f}\")\n", "    print(f\"   Top 10% Players: {(df_pubg['winPlacePerc'] >= 0.9).sum():,} ({(df_pubg['winPlacePerc'] >= 0.9).mean()*100:.1f}%)\")\n", "    print(f\"   Top 25% Players: {(df_pubg['winPlacePerc'] >= 0.75).sum():,} ({(df_pubg['winPlacePerc'] >= 0.75).mean()*100:.1f}%)\")\n", "\n", "if 'damageDealt' in df_pubg.columns:\n", "    print(f\"   Average Damage per Match: {df_pubg['damageDealt'].mean():.0f}\")\n", "    print(f\"   Maximum Damage in Single Match: {df_pubg['damageDealt'].max():.0f}\")\n", "\n", "# Match type distribution\n", "if 'matchType' in df_pubg.columns:\n", "    print(\"\\n[MATCH TYPE DISTRIBUTION]\")\n", "    match_distribution = df_pubg['matchType'].value_counts()\n", "    total_matches = len(df_pubg)\n", "    \n", "    for match_type, count in match_distribution.items():\n", "        percentage = (count / total_matches) * 100\n", "        print(f\"   {match_type}: {count:,} matches ({percentage:.1f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Interactive Visualization Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_interactive_count_plots(df, columns, title_prefix):\n", "    \"\"\"\n", "    Create interactive count plots for discrete features.\n", "    \n", "    Features:\n", "    - Interactive Plotly visualizations\n", "    - Multi-panel subplot layouts\n", "    - Professional hover information\n", "    \"\"\"\n", "    \n", "    # Verify columns exist in dataframe\n", "    missing_cols = [col for col in columns if col not in df.columns]\n", "    if missing_cols:\n", "        print(f\"[ERROR] Missing columns in dataset: {missing_cols}\")\n", "        return None\n", "    \n", "    # Calculate subplot layout\n", "    n_cols = len(columns)\n", "    rows = (n_cols + 2) // 3  # 3 columns per row\n", "    cols = min(3, n_cols)\n", "    \n", "    # Create subplots\n", "    fig = make_subplots(\n", "        rows=rows, cols=cols,\n", "        subplot_titles=[f'{col.title()} Distribution' for col in columns],\n", "        vertical_spacing=0.12,\n", "        horizontal_spacing=0.1\n", "    )\n", "    \n", "    # Add count plots for each column\n", "    for i, col in enumerate(columns):\n", "        row = (i // 3) + 1\n", "        col_pos = (i % 3) + 1\n", "        \n", "        # Get value counts (limit to top 15 for readability)\n", "        value_counts = df[col].value_counts().sort_index().head(15)\n", "        \n", "        # Add bar trace\n", "        fig.add_trace(\n", "            go.Bar(\n", "                x=value_counts.index,\n", "                y=value_counts.values,\n", "                name=col,\n", "                marker_color=px.colors.qualitative.Set3[i % len(px.colors.qualitative.Set3)],\n", "                hovertemplate=f'<b>{col.title()}</b>: %{{x}}<br>' +\n", "                             '<b>Count</b>: %{{y:,}}<br>' +\n", "                             '<b>Percentage</b>: %{{customdata:.1f}}%<extra></extra>',\n", "                customdata=(value_counts.values / len(df) * 100),\n", "                showlegend=False\n", "            ),\n", "            row=row, col=col_pos\n", "        )\n", "    \n", "    # Update layout\n", "    fig.update_layout(\n", "        title={\n", "            'text': f'{title_prefix} - Interactive Distribution Analysis',\n", "            'x': 0.5,\n", "            'xanchor': 'center',\n", "            'font': {'size': 18, 'family': 'Arial Black'}\n", "        },\n", "        height=300 * rows,\n", "        template='plotly_white'\n", "    )\n", "    \n", "    return fig\n", "\n", "print(\"[SUCCESS] Interactive count plot function defined\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Combat Features Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define combat features\n", "discrete_cols_combat = ['kills', 'assists', 'DBNOs', 'headshotKills', 'teamKills', 'roadKills']\n", "\n", "print(\"Combat Features Analysis\")\n", "print(f\"Features: {discrete_cols_combat}\")\n", "\n", "# Create interactive count plots for combat features\n", "combat_fig = create_interactive_count_plots(df_pubg, discrete_cols_combat, \"Combat Features\")\n", "\n", "if combat_fig is not None:\n", "    combat_fig.show()\n", "    print(\"[SUCCESS] Combat features visualization displayed\")\n", "else:\n", "    print(\"[ERROR] Failed to create combat features visualization\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Item Features Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define item features\n", "discrete_cols_items = ['heals', 'boosts', 'weaponsAcquired', 'vehicleDestroys']\n", "\n", "print(\"Item Features Analysis\")\n", "print(f\"Features: {discrete_cols_items}\")\n", "\n", "# Create interactive count plots for item features\n", "items_fig = create_interactive_count_plots(df_pubg, discrete_cols_items, \"Item Usage Features\")\n", "\n", "if items_fig is not None:\n", "    items_fig.show()\n", "    print(\"[SUCCESS] Item features visualization displayed\")\n", "else:\n", "    print(\"[ERROR] Failed to create item features visualization\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Correlation Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_interactive_correlation_heatmap(df, sample_size=10000):\n", "    \"\"\"\n", "    Create interactive correlation heatmap.\n", "    \n", "    Features:\n", "    - Interactive correlation analysis\n", "    - Performance optimization with sampling\n", "    - Professional heatmap styling\n", "    \"\"\"\n", "    \n", "    print(f\"Computing correlations for sample of {sample_size:,} records...\")\n", "    \n", "    # Select numerical features (exclude identifiers)\n", "    numerical_cols = df.select_dtypes(include=[np.number]).columns.tolist()\n", "    analysis_cols = [col for col in numerical_cols if 'id' not in col.lower()]\n", "    \n", "    # Sample data for performance\n", "    sample_df = df[analysis_cols].sample(n=min(sample_size, len(df)), random_state=42)\n", "    \n", "    # Compute correlation matrix\n", "    corr_matrix = sample_df.corr()\n", "    \n", "    # Create interactive heatmap\n", "    fig = go.Figure(data=go.Heatmap(\n", "        z=corr_matrix.values,\n", "        x=corr_matrix.columns,\n", "        y=corr_matrix.columns,\n", "        colorscale='RdBu',\n", "        zmid=0,\n", "        hovertemplate='<b>%{x}</b> vs <b>%{y}</b><br>' +\n", "                     'Correlation: %{z:.3f}<extra></extra>',\n", "        colorbar=dict(\n", "            title=\"Correlation Coefficient\",\n", "            titleside=\"right\"\n", "        )\n", "    ))\n", "    \n", "    # Update layout\n", "    fig.update_layout(\n", "        title={\n", "            'text': 'Interactive Correlation Matrix - PUBG Features',\n", "            'x': 0.5,\n", "            'xanchor': 'center',\n", "            'font': {'size': 18, 'family': 'Arial Black'}\n", "        },\n", "        width=800,\n", "        height=800,\n", "        template='plotly_white'\n", "    )\n", "    \n", "    print(f\"Correlation heatmap created for {len(analysis_cols)} features\")\n", "    \n", "    return fig\n", "\n", "# Generate and display correlation heatmap\n", "print(\"Correlation Heatmap Analysis\")\n", "correlation_fig = create_interactive_correlation_heatmap(df_pubg)\n", "\n", "if correlation_fig is not None:\n", "    correlation_fig.show()\n", "    print(\"[SUCCESS] Correlation heatmap visualization displayed\")\n", "else:\n", "    print(\"[ERROR] Failed to create correlation heatmap visualization\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Multi-Dimensional Scatter Plot"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_advanced_scatter_plot(df, sample_size=5000):\n", "    \"\"\"\n", "    Create advanced multi-dimensional scatter plot with bubble sizing and color mapping.\n", "    \n", "    Features:\n", "    - Multi-dimensional data visualization\n", "    - Interactive bubble charts\n", "    - Advanced color mapping and sizing\n", "    \"\"\"\n", "    \n", "    print(f\"Creating advanced scatter plot with {sample_size:,} samples...\")\n", "    \n", "    # Sample data for performance\n", "    sample_df = df.sample(n=min(sample_size, len(df)), random_state=42)\n", "    \n", "    # Create scatter plot with multiple dimensions\n", "    fig = go.Figure()\n", "    \n", "    # Add scatter trace\n", "    fig.add_trace(go.<PERSON>(\n", "        x=sample_df['walkDistance'],\n", "        y=sample_df['damageDealt'],\n", "        mode='markers',\n", "        marker=dict(\n", "            size=(sample_df['kills'] + 1) * 8,  # Size based on kills\n", "            color=sample_df['winPlacePerc'],    # Color based on win placement\n", "            colorscale='Viridis',\n", "            opacity=0.7,\n", "            line=dict(width=1, color='black'),\n", "            colorbar=dict(\n", "                title=\"Win Placement Percentile\",\n", "                titleside=\"right\"\n", "            )\n", "        ),\n", "        hovertemplate='<b>Walk Distance</b>: %{x:.0f}m<br>' +\n", "                     '<b>Damage Dealt</b>: %{y:.0f}<br>' +\n", "                     '<b>Kills</b>: %{customdata[0]}<br>' +\n", "                     '<b>Win Placement</b>: %{customdata[1]:.3f}<extra></extra>',\n", "        customdata=np.column_stack((sample_df['kills'], sample_df['winPlacePerc'])),\n", "        name='Players'\n", "    ))\n", "    \n", "    # Update layout\n", "    fig.update_layout(\n", "        title={\n", "            'text': 'Multi-Dimensional Player Performance Analysis<br><sub>Bubble size = Kills | Color = Win Placement</sub>',\n", "            'x': 0.5,\n", "            'xanchor': 'center',\n", "            'font': {'size': 16, 'family': 'Arial Black'}\n", "        },\n", "        xaxis_title='Walk Distance (meters)',\n", "        yaxis_title='Damage Dealt',\n", "        width=900,\n", "        height=600,\n", "        template='plotly_white'\n", "    )\n", "    \n", "    print(\"Advanced scatter plot created\")\n", "    \n", "    return fig\n", "\n", "# Generate and display advanced scatter plot\n", "print(\"Advanced Scatter Plot Analysis\")\n", "scatter_fig = create_advanced_scatter_plot(df_pubg)\n", "\n", "if scatter_fig is not None:\n", "    scatter_fig.show()\n", "    print(\"[SUCCESS] Advanced scatter plot visualization displayed\")\n", "else:\n", "    print(\"[ERROR] Failed to create scatter plot visualization\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Distribution Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_interactive_distribution_plots(df, continuous_features, sample_size=10000):\n", "    \"\"\"\n", "    Create interactive distribution plots with histograms.\n", "    \n", "    Features:\n", "    - Interactive distribution analysis\n", "    - Multi-panel subplot layouts\n", "    - Professional styling\n", "    \"\"\"\n", "    \n", "    print(f\"Creating distribution plots for {len(continuous_features)} features...\")\n", "    \n", "    # Sample data for performance\n", "    sample_df = df[continuous_features].sample(n=min(sample_size, len(df)), random_state=42)\n", "    \n", "    # Calculate subplot layout\n", "    n_features = len(continuous_features)\n", "    rows = (n_features + 2) // 3  # 3 columns per row\n", "    cols = min(3, n_features)\n", "    \n", "    # Create subplots\n", "    fig = make_subplots(\n", "        rows=rows, cols=cols,\n", "        subplot_titles=[f'{feature.title()} Distribution' for feature in continuous_features],\n", "        vertical_spacing=0.12,\n", "        horizontal_spacing=0.1\n", "    )\n", "    \n", "    # Add distribution plots for each feature\n", "    for i, feature in enumerate(continuous_features):\n", "        row = (i // 3) + 1\n", "        col_pos = (i % 3) + 1\n", "        \n", "        # Get feature data\n", "        feature_data = sample_df[feature].dropna()\n", "        \n", "        # Add histogram\n", "        fig.add_trace(\n", "            go.Histogram(\n", "                x=feature_data,\n", "                nbinsx=50,\n", "                name=f'{feature} Histogram',\n", "                opacity=0.7,\n", "                marker_color=px.colors.qualitative.Set2[i % len(px.colors.qualitative.Set2)],\n", "                hovertemplate=f'<b>{feature.title()}</b><br>' +\n", "                             'Range: %{x}<br>' +\n", "                             'Count: %{y}<extra></extra>',\n", "                showlegend=False\n", "            ),\n", "            row=row, col=col_pos\n", "        )\n", "    \n", "    # Update layout\n", "    fig.update_layout(\n", "        title={\n", "            'text': 'Interactive Feature Distribution Analysis',\n", "            'x': 0.5,\n", "            'xanchor': 'center',\n", "            'font': {'size': 18, 'family': 'Arial Black'}\n", "        },\n", "        height=300 * rows,\n", "        template='plotly_white'\n", "    )\n", "    \n", "    print(f\"Distribution plots created for {len(continuous_features)} features\")\n", "    \n", "    return fig\n", "\n", "# Define continuous features\n", "continuous_features = ['walkDistance', 'rideDistance', 'swimDistance', 'damageDealt', \n", "                      'longestKill', 'matchDuration']\n", "\n", "print(\"Distribution Analysis\")\n", "print(f\"Features: {continuous_features}\")\n", "\n", "# Generate distribution plots\n", "distribution_fig = create_interactive_distribution_plots(df_pubg, continuous_features)\n", "\n", "if distribution_fig is not None:\n", "    distribution_fig.show()\n", "    print(\"[SUCCESS] Distribution analysis visualization displayed\")\n", "else:\n", "    print(\"[ERROR] Failed to create distribution analysis visualization\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. Performance Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def categorize_performance(win_place_perc):\n", "    \"\"\"\n", "    Categorize player performance based on win placement percentile.\n", "    \"\"\"\n", "    if win_place_perc >= 0.9:\n", "        return 'Elite (Top 10%)'\n", "    elif win_place_perc >= 0.7:\n", "        return 'Advanced (Top 30%)'\n", "    elif win_place_perc >= 0.5:\n", "        return 'Intermediate (Top 50%)'\n", "    else:\n", "        return 'Be<PERSON>ner (Bottom 50%)'\n", "\n", "# Apply performance categorization\n", "df_pubg['performance_tier'] = df_pubg['winPlacePerc'].apply(categorize_performance)\n", "\n", "# Analyze performance distribution\n", "performance_distribution = df_pubg['performance_tier'].value_counts()\n", "\n", "print(\"PLAYER PERFORMANCE SEGMENTATION\")\n", "print(\"-\" * 40)\n", "\n", "for tier, count in performance_distribution.items():\n", "    percentage = (count / len(df_pubg)) * 100\n", "    print(f\"   {tier}: {count:,} players ({percentage:.1f}%)\")\n", "\n", "# Create performance tier visualization\n", "def create_performance_tier_analysis(df):\n", "    \"\"\"\n", "    Create interactive analysis of performance tiers.\n", "    \"\"\"\n", "    \n", "    # Create pie chart for performance distribution\n", "    performance_counts = df['performance_tier'].value_counts()\n", "    \n", "    fig = go.Figure(data=[go.Pie(\n", "        labels=performance_counts.index,\n", "        values=performance_counts.values,\n", "        hole=0.4,\n", "        hovertemplate='<b>%{label}</b><br>' +\n", "                     'Players: %{value:,}<br>' +\n", "                     'Percentage: %{percent}<extra></extra>',\n", "        marker_colors=px.colors.qualitative.Set3\n", "    )])\n", "    \n", "    fig.update_layout(\n", "        title={\n", "            'text': 'Player Performance Tier Distribution',\n", "            'x': 0.5,\n", "            'xanchor': 'center',\n", "            'font': {'size': 18, 'family': 'Arial Black'}\n", "        },\n", "        width=600,\n", "        height=500,\n", "        template='plotly_white'\n", "    )\n", "    \n", "    return fig\n", "\n", "# Generate and display performance tier analysis\n", "performance_fig = create_performance_tier_analysis(df_pubg)\n", "performance_fig.show()\n", "\n", "print(\"[SUCCESS] Performance tier analysis visualization displayed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 12. Final Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate key gaming statistics\n", "print(\"\\nFINAL ANALYSIS SUMMARY\")\n", "print(\"=\" * 50)\n", "\n", "# Performance insights\n", "elite_players = (df_pubg['performance_tier'] == 'Elite (Top 10%)').sum()\n", "elite_percentage = (elite_players / len(df_pubg)) * 100\n", "print(f\"Elite Players: {elite_players:,} ({elite_percentage:.1f}%)\")\n", "\n", "# Combat statistics\n", "avg_kills = df_pubg['kills'].mean()\n", "avg_damage = df_pubg['damageDealt'].mean()\n", "print(f\"Average Kills per Match: {avg_kills:.2f}\")\n", "print(f\"Average Damage per Match: {avg_damage:.0f}\")\n", "\n", "# Movement statistics\n", "if 'walkDistance' in df_pubg.columns:\n", "    avg_walk = df_pubg['walkDistance'].mean()\n", "    print(f\"Average Walk Distance: {avg_walk:.0f} meters\")\n", "\n", "# Survival statistics\n", "if 'heals' in df_pubg.columns:\n", "    avg_heals = df_pubg['heals'].mean()\n", "    print(f\"Average Heals Used: {avg_heals:.2f}\")\n", "\n", "print(\"\\n[SUCCESS] PUBG Game Data Analysis Completed\")\n", "print(f\"[INFO] Analyzed {len(df_pubg):,} player records\")\n", "print(f\"[INFO] Generated {len(analysis_features)} feature visualizations\")\n", "print(\"[INFO] All interactive visualizations are ready for exploration\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}