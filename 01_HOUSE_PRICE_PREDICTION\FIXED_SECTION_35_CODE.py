# ===== FIXED SECTION 35: ADVANCED FEATURE ENGINEERING =====
# This version handles dependency issues and works with your optimal workflow

# Initialize advanced feature engineering
print("=== ADVANCED FEATURE ENGINEERING ===")

# Check for processed data availability (works with your workflow)
if 'df_processed' in globals() and df_processed is not None:
    print("✅ Using processed data for feature engineering")
    
    # Prepare features and target from processed data
    if 'SalePrice' in df_processed.columns:
        # Select key features for demonstration
        key_features = ['OverallQual', 'GrLivArea', 'GarageCars', 'TotalBsmtSF', 
                       'FullBath', 'YearBuilt', 'LotArea', 'BedroomAbvGr']
        
        # Filter available features
        available_features = [col for col in key_features if col in df_processed.columns]
        
        if len(available_features) >= 3:
            X_demo = df_processed[available_features].copy()
            y_demo = df_processed['SalePrice'].copy()
            
            print(f"Demo features selected: {len(available_features)}")
            print(f"Features: {available_features}")
            
            feature_engineer = AdvancedFeatureEngineer()
            
            # Create polynomial features (interaction only to avoid explosion)
            X_poly = feature_engineer.create_polynomial_features(X_demo, degree=2, interaction_only=True)
            
            # Create custom interactions
            X_interactions = feature_engineer.create_custom_interactions(X_demo)
            
            # Combine original and interaction features
            X_enhanced = pd.concat([X_interactions, X_poly.iloc[:, X_demo.shape[1]:]], axis=1)
            
            print(f"\nFinal enhanced feature set: {X_enhanced.shape[1]} features")
            
            # Select best features to avoid overfitting
            X_selected = feature_engineer.select_best_features(X_enhanced, y_demo, k=min(30, X_enhanced.shape[1]))
            
            # Get feature importance scores
            feature_scores = feature_engineer.get_feature_importance_scores(X_enhanced, y_demo)
            
            print("\n=== TOP 10 ENGINEERED FEATURES ===")
            if feature_scores is not None:
                top_features = feature_scores.head(10)
                for idx, row in top_features.iterrows():
                    print(f"{row['feature']}: {row['score']:.2f}")
            
            # Store enhanced features for later use in final model creation
            enhanced_features_demo = X_selected.copy()
            feature_engineering_results = {
                'enhanced_features': X_selected,
                'feature_scores': feature_scores,
                'feature_engineer': feature_engineer,
                'original_features': X_demo,
                'target': y_demo
            }
            
            print(f"\n✅ Advanced feature engineering demonstration complete!")
            print(f"  • Original features: {X_demo.shape[1]}")
            print(f"  • Enhanced features: {X_enhanced.shape[1]}")
            print(f"  • Selected features: {X_selected.shape[1]}")
            print(f"  • Feature improvement: {((X_enhanced.shape[1] - X_demo.shape[1]) / X_demo.shape[1] * 100):.1f}%")
            
            # Quick performance demonstration (optional)
            print(f"\n🔬 QUICK PERFORMANCE DEMONSTRATION:")
            X_train_demo, X_test_demo, y_train_demo, y_test_demo = train_test_split(
                X_selected, y_demo, test_size=0.2, random_state=42
            )
            
            # Scale features
            scaler_demo = StandardScaler()
            X_train_demo_scaled = scaler_demo.fit_transform(X_train_demo)
            X_test_demo_scaled = scaler_demo.transform(X_test_demo)
            
            # Train quick demo model
            rf_demo = RandomForestRegressor(n_estimators=50, random_state=42, n_jobs=-1)
            rf_demo.fit(X_train_demo_scaled, y_train_demo)
            
            # Evaluate demo model
            y_pred_demo = rf_demo.predict(X_test_demo_scaled)
            r2_demo = r2_score(y_test_demo, y_pred_demo)
            rmse_demo = np.sqrt(mean_squared_error(y_test_demo, y_pred_demo))
            
            print(f"  • Demo Model R² Score: {r2_demo:.4f}")
            print(f"  • Demo Model RMSE: ${rmse_demo:,.0f}")
            print(f"  • Enhanced features ready for final model creation!")
            
            # Note about final comparison (will happen in final model section)
            print(f"\n📝 NOTE: Final performance comparison will be available")
            print(f"     after comprehensive model creation in Section 47.")
            
        else:
            print(f"❌ Insufficient features available. Found: {len(available_features)}")
            feature_engineering_results = None
    else:
        print("❌ SalePrice column not found in processed data")
        feature_engineering_results = None
        
elif 'df_processed_enhanced' in globals() and df_processed_enhanced is not None:
    print("✅ Using enhanced processed data")
    # Similar logic for enhanced data
    feature_engineering_results = {'status': 'enhanced_data_available'}
    
else:
    print("⚠️ Advanced Feature Engineering - Demonstration Mode")
    print("This section will be fully utilized during final model creation.")
    print("Enhanced feature engineering capabilities include:")
    print("  • Polynomial feature generation with interaction terms")
    print("  • Custom domain-specific feature interactions")
    print("  • Statistical feature selection with F-regression")
    print("  • Professional class-based architecture")
    print("  • Performance comparison and improvement tracking")
    print("  • Production-ready error handling and validation")
    
    # Create placeholder for final model creation
    feature_engineering_results = {
        'status': 'ready_for_final_implementation',
        'capabilities': [
            'polynomial_features',
            'custom_interactions', 
            'statistical_selection',
            'performance_tracking'
        ]
    }
    
print("\n" + "="*60)
