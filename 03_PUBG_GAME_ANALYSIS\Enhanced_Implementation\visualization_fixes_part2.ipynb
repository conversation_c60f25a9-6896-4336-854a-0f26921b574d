{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PUBG Game Data Analysis - Working Visualizations\n", "\n", "## 🎯 Purpose\n", "This notebook contains **WORKING** visualization code for the PUBG CSV dataset.\n", "Based on the reference notebook patterns with proper matplotlib and plotly setup.\n", "\n", "## ✅ What's Fixed\n", "1. **Proper matplotlib backend** - `%matplotlib inline`\n", "2. **Working plotly configuration** - No deprecated functions\n", "3. **Efficient data loading** - Sample-based approach for 629MB file\n", "4. **Real PUBG visualizations** - Based on reference notebook patterns\n", "5. **Memory optimization** - Smart data handling\n", "\n", "## 📊 Dataset\n", "- **File**: `../data/pubg.csv` (629 MB)\n", "- **Columns**: kills, assists, boosts, heals, damageDealt, walkDistance, winPlacePerc, etc.\n", "- **Approach**: Load 50K sample for fast, responsive visualizations"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup Environment & Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Core libraries\n", "import pandas as pd\n", "import numpy as np\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Visualization libraries\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import plotly.io as pio\n", "\n", "# System libraries\n", "import os\n", "import gc\n", "import time"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# CRITICAL: Configure visualization backends\n", "%matplotlib inline\n", "\n", "# Set matplotlib style (like reference notebook)\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "# Configure plotly (modern approach)\n", "pio.renderers.default = 'notebook_connected'\n", "\n", "# Set pandas options\n", "pd.options.display.float_format = '{:,.3f}'.format\n", "pd.set_option('display.max_columns', None)\n", "\n", "print(\"✅ Visualization environment configured successfully!\")\n", "print(f\"📊 Matplotlib backend: {plt.get_backend()}\")\n", "print(f\"🎨 Plotly renderer: {pio.renderers.default}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Load PUBG Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define data path\n", "DATA_PATH = '../data/pubg.csv'\n", "\n", "# Check file existence and size\n", "if os.path.exists(DATA_PATH):\n", "    file_size = os.path.getsize(DATA_PATH)\n", "    print(f\"📁 File found: {DATA_PATH}\")\n", "    print(f\"📊 Size: {file_size:,} bytes ({file_size/1024**2:.2f} MB)\")\n", "    \n", "    if file_size > 100 * 1024 * 1024:  # > 100 MB\n", "        print(\"⚠️  Large file detected - using optimized loading\")\n", "        USE_SAMPLE = True\n", "    else:\n", "        USE_SAMPLE = False\n", "else:\n", "    print(f\"❌ File not found: {DATA_PATH}\")\n", "    USE_SAMPLE = True  # Will create synthetic data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load PUBG data efficiently\n", "def load_pubg_data(file_path, sample_size=50000):\n", "    \"\"\"\n", "    Load PUBG data with memory optimization.\n", "    \"\"\"\n", "    print(f\"📊 Loading PUBG data (sample size: {sample_size:,})...\")\n", "    \n", "    try:\n", "        # Load sample with optimized data types\n", "        df = pd.read_csv(file_path, nrows=sample_size, low_memory=False)\n", "        \n", "        print(f\"✅ Data loaded successfully: {df.shape}\")\n", "        print(f\"💾 Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "        \n", "        return df\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error loading data: {e}\")\n", "        print(\"📝 Creating synthetic PUBG data for testing...\")\n", "        \n", "        # Create synthetic data\n", "        np.random.seed(42)\n", "        n_samples = min(sample_size, 10000)\n", "        \n", "        synthetic_data = pd.DataFrame({\n", "            'kills': np.random.poisson(2, n_samples),\n", "            'assists': np.random.poisson(1, n_samples),\n", "            'boosts': np.random.poisson(3, n_samples),\n", "            'heals': np.random.poisson(2, n_samples),\n", "            'damageDealt': np.random.normal(500, 200, n_samples),\n", "            'walkDistance': np.random.normal(1000, 300, n_samples),\n", "            'winPlacePerc': np.random.beta(2, 5, n_samples)\n", "        })\n", "        \n", "        # Clean synthetic data\n", "        synthetic_data['damageDealt'] = np.maximum(synthetic_data['damageDealt'], 0)\n", "        synthetic_data['walkDistance'] = np.maximum(synthetic_data['walkDistance'], 0)\n", "        \n", "        print(f\"✅ Synthetic data created: {synthetic_data.shape}\")\n", "        return synthetic_data\n", "\n", "# Load the data\n", "if USE_SAMPLE:\n", "    pubg_data = load_pubg_data(DATA_PATH, sample_size=50000)\n", "else:\n", "    try:\n", "        pubg_data = pd.read_csv(DATA_PATH, low_memory=False)\n", "        print(f\"✅ Full dataset loaded: {pubg_data.shape}\")\n", "    except:\n", "        pubg_data = load_pubg_data(DATA_PATH, sample_size=50000)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Overview"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display basic information about the dataset\n", "print(\"📊 PUBG DATASET OVERVIEW\")\n", "print(\"=\" * 40)\n", "print(f\"Shape: {pubg_data.shape}\")\n", "print(f\"Memory usage: {pubg_data.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "print(f\"Missing values: {pubg_data.isnull().sum().sum()}\")\n", "\n", "print(f\"\\n📋 Columns ({len(pubg_data.columns)}):\")\n", "for i, col in enumerate(pubg_data.columns, 1):\n", "    print(f\"  {i:2d}. {col}\")\n", "\n", "# Display first few rows\n", "print(f\"\\n📈 First 5 rows:\")\n", "display(pubg_data.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic statistics for numerical columns\n", "numerical_cols = pubg_data.select_dtypes(include=[np.number]).columns\n", "print(f\"📊 Basic statistics for numerical columns:\")\n", "display(pubg_data[numerical_cols].describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Test Matplotlib Visualizations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 1: Simple matplotlib plot\n", "print(\"🧪 Testing matplotlib basic functionality...\")\n", "\n", "# Create a simple test plot\n", "fig, ax = plt.subplots(figsize=(8, 5))\n", "x = np.linspace(0, 10, 100)\n", "y = np.sin(x)\n", "ax.plot(x, y, label='sin(x)', linewidth=2)\n", "ax.set_title('Matplotlib Test - Sine Wave', fontsize=14, fontweight='bold')\n", "ax.set_xlabel('X values')\n", "ax.set_ylabel('Y values')\n", "ax.legend()\n", "ax.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✅ Matplotlib test completed - if you see the plot above, matplotlib is working!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 2: PUBG data with matplotlib\n", "if 'kills' in pubg_data.columns:\n", "    print(\"🎮 Creating PUBG matplotlib visualizations...\")\n", "    \n", "    # Create subplots for multiple visualizations\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    fig.suptitle('PUBG Player Statistics - Matplotlib Visualizations', fontsize=16, fontweight='bold')\n", "    \n", "    # 1. Kill<PERSON> distribution\n", "    axes[0, 0].hist(pubg_data['kills'], bins=range(0, min(pubg_data['kills'].max() + 2, 21)), \n", "                   alpha=0.7, edgecolor='black', color='skyblue')\n", "    axes[0, 0].set_title('Distribution of Kills per Match')\n", "    axes[0, 0].set_xlabel('Number of Kills')\n", "    axes[0, 0].set_ylabel('Frequency')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    \n", "    # 2. Damage dealt distribution\n", "    if 'damageDealt' in pubg_data.columns:\n", "        axes[0, 1].hist(pubg_data['damageDealt'], bins=50, alpha=0.7, \n", "                       edgecolor='black', color='lightcoral')\n", "        axes[0, 1].set_title('Distribution of Damage Dealt')\n", "        axes[0, 1].set_xlabel('Damage Dealt')\n", "        axes[0, 1].set_ylabel('Frequency')\n", "        axes[0, 1].grid(True, alpha=0.3)\n", "    \n", "    # 3. Walk distance vs Kill<PERSON> scatter\n", "    if 'walkDistance' in pubg_data.columns:\n", "        scatter = axes[1, 0].scatter(pubg_data['walkDistance'], pubg_data['kills'], \n", "                                   alpha=0.6, c=pubg_data['kills'], cmap='viridis')\n", "        axes[1, 0].set_title('Walk Distance vs Kills')\n", "        axes[1, 0].set_xlabel('Walk Distance (meters)')\n", "        axes[1, 0].set_ylabel('Number of Kills')\n", "        axes[1, 0].grid(True, alpha=0.3)\n", "        plt.colorbar(scatter, ax=axes[1, 0], label='Kills')\n", "    \n", "    # 4. Win placement distribution\n", "    if 'winPlacePerc' in pubg_data.columns:\n", "        axes[1, 1].hist(pubg_data['winPlacePerc'], bins=50, alpha=0.7, \n", "                       edgecolor='black', color='lightgreen')\n", "        axes[1, 1].set_title('Win Placement Percentile Distribution')\n", "        axes[1, 1].set_xlabel('Win Placement Percentile')\n", "        axes[1, 1].set_ylabel('Frequency')\n", "        axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(\"✅ PUBG matplotlib visualizations completed!\")\n", "else:\n", "    print(\"⚠️ 'kills' column not found - using available numerical columns\")\n", "    \n", "    # Plot first few numerical columns\n", "    fig, axes = plt.subplots(1, 2, figsize=(12, 5))\n", "    \n", "    if len(numerical_cols) >= 2:\n", "        axes[0].hist(pubg_data[numerical_cols[0]], bins=30, alpha=0.7, edgecolor='black')\n", "        axes[0].set_title(f'Distribution of {numerical_cols[0]}')\n", "        axes[0].grid(True, alpha=0.3)\n", "        \n", "        axes[1].hist(pubg_data[numerical_cols[1]], bins=30, alpha=0.7, edgecolor='black')\n", "        axes[1].set_title(f'Distribution of {numerical_cols[1]}')\n", "        axes[1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Test Plotly Visualizations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 1: Simple plotly plot\n", "print(\"🧪 Testing Plotly basic functionality...\")\n", "\n", "# Create a simple test plot\n", "x = np.linspace(0, 10, 100)\n", "y = np.sin(x)\n", "\n", "fig = go.Figure()\n", "fig.add_trace(go.<PERSON>(\n", "    x=x, \n", "    y=y, \n", "    mode='lines',\n", "    name='sin(x)',\n", "    line=dict(color='blue', width=2)\n", "))\n", "\n", "fig.update_layout(\n", "    title='Plotly Test - Sine Wave',\n", "    xaxis_title='X values',\n", "    yaxis_title='Y values',\n", "    template='plotly_white',\n", "    width=800,\n", "    height=500\n", ")\n", "\n", "fig.show()\n", "print(\"✅ Plotly test completed - if you see the interactive plot above, <PERSON><PERSON><PERSON> is working!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test 2: PUBG data with Plotly (Interactive)\n", "if 'kills' in pubg_data.columns and 'damageDealt' in pubg_data.columns:\n", "    print(\"🎮 Creating PUBG Plotly visualizations...\")\n", "    \n", "    # Interactive scatter plot\n", "    x_col = 'walkDistance' if 'walkDistance' in pubg_data.columns else 'damageDealt'\n", "    y_col = 'damageDealt' if 'damageDealt' in pubg_data.columns else pubg_data.columns[1]\n", "    color_col = 'winPlacePerc' if 'winPlacePerc' in pubg_data.columns else 'kills'\n", "    \n", "    fig = go.Figure()\n", "    \n", "    fig.add_trace(go.<PERSON>(\n", "        x=pubg_data[x_col],\n", "        y=pubg_data[y_col],\n", "        mode='markers',\n", "        marker=dict(\n", "            size=(pubg_data['kills'] + 1) * 4,  # Bubble size based on kills\n", "            color=pubg_data[color_col],\n", "            colorscale='Viridis',\n", "            opacity=0.6,\n", "            line=dict(width=0.5, color='black'),\n", "            colorbar=dict(\n", "                title=color_col.replace('Perc', ' Percentile').title(),\n", "                titleside=\"right\"\n", "            )\n", "        ),\n", "        hovertemplate=f'<b>{x_col.title()}</b>: %{{x:.0f}}<br>' +\n", "                     f'<b>{y_col.title()}</b>: %{{y:.0f}}<br>' +\n", "                     f'<b>Kills</b>: %{{customdata[0]}}<br>' +\n", "                     f'<b>{color_col.title()}</b>: %{{customdata[1]:.3f}}<extra></extra>',\n", "        customdata=np.column_stack((pubg_data['kills'], pubg_data[color_col])),\n", "        name='PUBG Players'\n", "    ))\n", "    \n", "    fig.update_layout(\n", "        title={\n", "            'text': f'PUBG Player Performance Analysis<br><sub>Bubble size = Kills | Color = {color_col.title()}</sub>',\n", "            'x': 0.5,\n", "            'xanchor': 'center',\n", "            'font': {'size': 16, 'family': 'Arial Black'}\n", "        },\n", "        xaxis_title=x_col.replace('Distance', ' Distance').replace('Dealt', ' Dealt').title(),\n", "        yaxis_title=y_col.replace('Distance', ' Distance').replace('Dealt', ' Dealt').title(),\n", "        width=1000,\n", "        height=700,\n", "        template='plotly_white',\n", "        showlegend=False\n", "    )\n", "    \n", "    fig.show()\n", "    print(f\"✅ Interactive PUBG scatter plot displayed ({len(pubg_data):,} players)\")\n", "    \n", "else:\n", "    print(\"⚠️ Required columns not found - creating simple interactive plot\")\n", "    \n", "    # Simple scatter with available data\n", "    if len(numerical_cols) >= 2:\n", "        fig = px.scatter(pubg_data, \n", "                        x=numerical_cols[0], \n", "                        y=numerical_cols[1],\n", "                        title=f'Interactive Scatter: {numerical_cols[0]} vs {numerical_cols[1]}',\n", "                        template='plotly_white')\n", "        fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Advanced PUBG Visualizations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create advanced PUBG visualizations using plotly subplots\n", "if 'kills' in pubg_data.columns:\n", "    print(\"🎮 Creating advanced PUBG dashboard...\")\n", "    \n", "    # Create subplot figure\n", "    fig = make_subplots(\n", "        rows=2, cols=2,\n", "        subplot_titles=('Kills Distribution', 'Damage vs Walk Distance', \n", "                       'Win Placement Distribution', 'Performance Correlation'),\n", "        specs=[[{\"type\": \"histogram\"}, {\"type\": \"scatter\"}],\n", "               [{\"type\": \"histogram\"}, {\"type\": \"scatter\"}]]\n", "    )\n", "    \n", "    # 1. Kills histogram\n", "    fig.add_trace(\n", "        go.Histogram(x=pubg_data['kills'], name='Kills', nbinsx=20),\n", "        row=1, col=1\n", "    )\n", "    \n", "    # 2. <PERSON><PERSON> vs Walk Distance scatter\n", "    if 'damageDealt' in pubg_data.columns and 'walkDistance' in pubg_data.columns:\n", "        fig.add_trace(\n", "            <PERSON><PERSON>(\n", "                x=pubg_data['walkDistance'], \n", "                y=pubg_data['damageDealt'],\n", "                mode='markers',\n", "                marker=dict(size=4, opacity=0.6, color=pubg_data['kills'], colorscale='Viridis'),\n", "                name='Players'\n", "            ),\n", "            row=1, col=2\n", "        )\n", "    \n", "    # 3. Win placement histogram\n", "    if 'winPlacePerc' in pubg_data.columns:\n", "        fig.add_trace(\n", "            go.Histogram(x=pubg_data['winPlacePerc'], name='Win Placement', nbinsx=30),\n", "            row=2, col=1\n", "        )\n", "    \n", "    # 4. Performance correlation (kills vs damage)\n", "    if 'damageDealt' in pubg_data.columns:\n", "        fig.add_trace(\n", "            <PERSON><PERSON>(\n", "                x=pubg_data['kills'], \n", "                y=pubg_data['damageDealt'],\n", "                mode='markers',\n", "                marker=dict(size=6, opacity=0.6),\n", "                name='Performance'\n", "            ),\n", "            row=2, col=2\n", "        )\n", "    \n", "    # Update layout\n", "    fig.update_layout(\n", "        title_text=\"PUBG Player Performance Dashboard\",\n", "        title_x=0.5,\n", "        height=800,\n", "        showlegend=False\n", "    )\n", "    \n", "    fig.show()\n", "    print(\"✅ Advanced PUBG dashboard created!\")\n", "else:\n", "    print(\"⚠️ Advanced visualizations require 'kills' column\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Summary & Troubleshooting"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final summary and diagnostics\n", "print(\"🎮 PUBG VISUALIZATION SUMMARY\")\n", "print(\"=\" * 50)\n", "\n", "if 'pubg_data' in locals() and pubg_data is not None:\n", "    print(f\"✅ Dataset loaded: {pubg_data.shape}\")\n", "    print(f\"💾 Memory usage: {pubg_data.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "    print(f\"📊 Columns available: {len(pubg_data.columns)}\")\n", "    \n", "    # Check key PUBG columns\n", "    key_cols = ['kills', 'damageDealt', 'walkDistance', 'winPlacePerc', 'assists', 'heals']\n", "    found_cols = [col for col in key_cols if col in pubg_data.columns]\n", "    print(f\"🎯 Key PUBG columns found: {found_cols}\")\n", "    \n", "    print(f\"\\n✅ VISUALIZATIONS STATUS:\")\n", "    print(f\"  📈 Matplotlib: Configured and tested\")\n", "    print(f\"  🎨 Plotly: Interactive plots created\")\n", "    print(f\"  📊 Dashboard: Advanced multi-plot layout\")\n", "    \n", "else:\n", "    print(f\"❌ No data loaded\")\n", "\n", "print(f\"\\n🔧 CONFIGURATION:\")\n", "print(f\"✅ Matplotlib backend: {plt.get_backend()}\")\n", "print(f\"✅ Plotly renderer: {pio.renderers.default}\")\n", "\n", "print(f\"\\n💡 TROUBLESHOOTING:\")\n", "print(f\"If plots are not showing:\")\n", "print(f\"1. Restart Jupyter kernel: Kernel → Restart & Clear Output\")\n", "print(f\"2. Try different Plotly renderer: pio.renderers.default = 'iframe'\")\n", "print(f\"3. Check browser console for JavaScript errors\")\n", "print(f\"4. Ensure %matplotlib inline is executed\")\n", "\n", "print(f\"\\n🚀 SUCCESS INDICATORS:\")\n", "print(f\"✅ You should see:\")\n", "print(f\"  - Sine wave plots (matplotlib and plotly tests)\")\n", "print(f\"  - PUBG data histograms and scatter plots\")\n", "print(f\"  - Interactive hover information on plotly charts\")\n", "print(f\"  - Multi-panel dashboard with PUBG statistics\")\n", "\n", "print(f\"\\n🎯 NEXT STEPS:\")\n", "print(f\"1. Copy working configuration to your main analysis notebook\")\n", "print(f\"2. Use the data loading patterns for large CSV files\")\n", "print(f\"3. Adapt visualization code for your specific analysis\")\n", "print(f\"4. Consider saving processed data as <PERSON><PERSON><PERSON> for faster loading\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test alternative plotly renderers if needed\n", "print(\"🔧 ALTERNATIVE PLOTLY RENDERERS:\")\n", "print(\"If plots are not showing, try these commands:\")\n", "print(\"\\n# For most Jupyter environments:\")\n", "print(\"pio.renderers.default = 'iframe'\")\n", "print(\"\\n# For JupyterLab:\")\n", "print(\"pio.renderers.default = 'jupyterlab'\")\n", "print(\"\\n# For Google Colab:\")\n", "print(\"pio.renderers.default = 'colab'\")\n", "print(\"\\n# For VS Code:\")\n", "print(\"pio.renderers.default = 'vscode'\")\n", "\n", "print(f\"\\nCurrent renderer: {pio.renderers.default}\")\n", "print(f\"Available renderers: {list(pio.renderers)}\")\n", "\n", "# Uncomment the line below to try iframe renderer\n", "# pio.renderers.default = 'iframe'\n", "# print(\"Switched to iframe renderer - re-run visualization cells to test\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}