# ===== SATELLITE IMAGES GENERATOR WITH ZIP DOWNLOAD =====
# Generate satellite images, display in notebook, and create downloadable ZIP

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from PIL import Image, ImageDraw, ImageFilter, ImageEnhance
import os
import zipfile
import random
from datetime import datetime
import json
from IPython.display import display, HTML
import warnings
warnings.filterwarnings('ignore')

print("🛰️ SATELLITE IMAGES GENERATOR WITH ZIP DOWNLOAD")
print("=" * 60)

class SatelliteImageGenerator:
    """Generate realistic satellite images based on house features"""
    
    def __init__(self, image_size=(512, 512)):
        self.image_size = image_size
        self.images_generated = 0
        self.image_metadata = []
        
    def generate_property_image(self, house_features, property_id):
        """Generate a realistic satellite image for a property"""
        
        # Create base image with grass/terrain
        img = Image.new('RGB', self.image_size, color=(34, 139, 34))  # Green base
        draw = ImageDraw.Draw(img)
        
        # Extract house features
        living_area = house_features.get('GrLivArea', 1500)
        overall_qual = house_features.get('OverallQual', 5)
        year_built = house_features.get('YearBuilt', 1980)
        garage_cars = house_features.get('GarageCars', 0)
        lot_area = house_features.get('LotArea', 8000)
        total_bsmt_sf = house_features.get('TotalBsmtSF', 0)
        
        # Calculate house size based on living area
        house_size = min(200, max(80, int(living_area / 12)))
        
        # House position (center with some variation)
        house_x = self.image_size[0] // 2 - house_size // 2 + random.randint(-30, 30)
        house_y = self.image_size[1] // 2 - house_size // 2 + random.randint(-30, 30)
        
        # House color based on year and quality
        if year_built > 2000:
            house_color = (139, 69, 19) if overall_qual > 7 else (160, 82, 45)
        else:
            house_color = (160, 82, 45) if overall_qual > 6 else (139, 90, 43)
        
        # Draw main house
        draw.rectangle([house_x, house_y, house_x + house_size, house_y + house_size], 
                      fill=house_color, outline=(0, 0, 0), width=2)
        
        # Add roof details
        roof_color = (105, 105, 105) if overall_qual > 7 else (139, 69, 19)
        draw.polygon([(house_x, house_y), 
                     (house_x + house_size//2, house_y - 20),
                     (house_x + house_size, house_y)], fill=roof_color)
        
        # Add garage if present
        if garage_cars > 0:
            garage_width = min(80, garage_cars * 25)
            garage_height = 40
            garage_x = house_x + house_size + 10
            garage_y = house_y + house_size // 3
            
            draw.rectangle([garage_x, garage_y, garage_x + garage_width, garage_y + garage_height],
                          fill=(105, 105, 105), outline=(0, 0, 0), width=1)
            
            # Garage door
            door_width = garage_width // garage_cars
            for i in range(garage_cars):
                door_x = garage_x + i * door_width + 2
                draw.rectangle([door_x, garage_y + 5, door_x + door_width - 4, garage_y + garage_height - 5],
                              fill=(80, 80, 80))
        
        # Add driveway
        if garage_cars > 0:
            driveway_points = [(house_x + house_size, house_y + house_size//2),
                              (garage_x, garage_y + garage_height//2),
                              (garage_x, garage_y + garage_height),
                              (house_x + house_size, house_y + house_size//2 + 20)]
            draw.polygon(driveway_points, fill=(128, 128, 128))
        
        # Add pool for high-quality houses
        if overall_qual >= 8 and random.random() > 0.6:
            pool_size = 40 + (overall_qual - 8) * 10
            pool_x = house_x - pool_size - 20
            pool_y = house_y + house_size + 15
            
            if pool_x > 20 and pool_y + pool_size < self.image_size[1] - 20:
                draw.ellipse([pool_x, pool_y, pool_x + pool_size, pool_y + pool_size//2], 
                            fill=(0, 191, 255), outline=(0, 0, 139), width=2)
                
                # Pool deck
                deck_margin = 8
                draw.ellipse([pool_x - deck_margin, pool_y - deck_margin, 
                             pool_x + pool_size + deck_margin, pool_y + pool_size//2 + deck_margin], 
                            fill=(222, 184, 135), outline=(139, 69, 19), width=1)
        
        # Add trees based on lot area and quality
        num_trees = min(12, max(3, int(lot_area / 1500) + overall_qual // 2))
        for _ in range(num_trees):
            tree_x = random.randint(30, self.image_size[0] - 30)
            tree_y = random.randint(30, self.image_size[1] - 30)
            
            # Avoid placing trees on house
            if not (house_x - 20 <= tree_x <= house_x + house_size + 20 and 
                   house_y - 20 <= tree_y <= house_y + house_size + 20):
                
                tree_size = random.randint(15, 25)
                tree_color = random.choice([(0, 100, 0), (34, 139, 34), (0, 128, 0)])
                
                # Tree crown
                draw.ellipse([tree_x - tree_size, tree_y - tree_size, 
                             tree_x + tree_size, tree_y + tree_size], 
                            fill=tree_color, outline=(0, 50, 0))
                
                # Tree trunk
                trunk_width = tree_size // 4
                draw.rectangle([tree_x - trunk_width, tree_y, 
                               tree_x + trunk_width, tree_y + tree_size], 
                              fill=(101, 67, 33))
        
        # Add pathways
        if random.random() > 0.5:
            path_points = [(house_x + house_size//2, house_y + house_size),
                          (house_x + house_size//2, self.image_size[1] - 20)]
            draw.line(path_points, fill=(169, 169, 169), width=8)
        
        # Add property boundaries (fence)
        if overall_qual > 6:
            fence_color = (139, 69, 19)
            # Top fence
            draw.line([(20, 20), (self.image_size[0] - 20, 20)], fill=fence_color, width=3)
            # Bottom fence
            draw.line([(20, self.image_size[1] - 20), (self.image_size[0] - 20, self.image_size[1] - 20)], 
                     fill=fence_color, width=3)
            # Left fence
            draw.line([(20, 20), (20, self.image_size[1] - 20)], fill=fence_color, width=3)
            # Right fence
            draw.line([(self.image_size[0] - 20, 20), (self.image_size[0] - 20, self.image_size[1] - 20)], 
                     fill=fence_color, width=3)
        
        # Add some texture and realism
        img = img.filter(ImageFilter.GaussianBlur(radius=0.5))
        
        # Enhance based on quality
        if overall_qual > 7:
            enhancer = ImageEnhance.Brightness(img)
            img = enhancer.enhance(1.1)
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(1.1)
        
        # Store metadata
        metadata = {
            'property_id': property_id,
            'living_area': living_area,
            'overall_quality': overall_qual,
            'year_built': year_built,
            'garage_cars': garage_cars,
            'lot_area': lot_area,
            'house_size_pixels': house_size,
            'has_pool': overall_qual >= 8 and random.random() > 0.6,
            'num_trees': num_trees,
            'generation_time': datetime.now().isoformat()
        }
        
        self.image_metadata.append(metadata)
        self.images_generated += 1
        
        return img, metadata

def create_satellite_images_with_zip(df_processed, num_images=20):
    """Generate satellite images and create ZIP file"""
    
    print(f"🎯 GENERATING {num_images} SATELLITE IMAGES")
    print("-" * 40)
    
    # Create directories
    images_dir = "satellite_images"
    if not os.path.exists(images_dir):
        os.makedirs(images_dir)
    
    # Initialize generator
    generator = SatelliteImageGenerator(image_size=(512, 512))
    
    # Select random houses for image generation
    if len(df_processed) < num_images:
        sample_indices = range(len(df_processed))
    else:
        sample_indices = random.sample(range(len(df_processed)), num_images)
    
    generated_images = []
    image_files = []
    
    print(f"📸 Generating images...")
    
    for i, idx in enumerate(sample_indices):
        house_data = df_processed.iloc[idx]
        property_id = f"PROP_{idx:04d}"
        
        # Generate image
        img, metadata = generator.generate_property_image(house_data.to_dict(), property_id)
        
        # Save image
        image_filename = f"{images_dir}/{property_id}_satellite.png"
        img.save(image_filename, 'PNG', quality=95)
        image_files.append(image_filename)
        
        # Store for display
        generated_images.append((img, metadata, image_filename))
        
        if (i + 1) % 5 == 0:
            print(f"  ✅ Generated {i + 1}/{num_images} images")
    
    print(f"✅ All {num_images} images generated!")
    
    # Create metadata file
    metadata_file = f"{images_dir}/image_metadata.json"
    with open(metadata_file, 'w') as f:
        json.dump(generator.image_metadata, f, indent=2)
    
    # Create ZIP file
    zip_filename = f"satellite_images_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
    
    print(f"\n📦 CREATING ZIP FILE: {zip_filename}")
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # Add all images
        for image_file in image_files:
            zipf.write(image_file, os.path.basename(image_file))
        
        # Add metadata
        zipf.write(metadata_file, "image_metadata.json")
        
        # Add README
        readme_content = f"""# Satellite Images Dataset
Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Total images: {num_images}
Image size: 512x512 pixels
Format: PNG

## Files:
- PROP_XXXX_satellite.png: Satellite images
- image_metadata.json: Detailed metadata for each image

## Metadata includes:
- Property ID
- Living area, quality, year built
- Garage cars, lot area
- Generated features (pool, trees, etc.)
- Generation timestamp

## Usage:
Extract the ZIP file and view images in any image viewer.
Use metadata.json for analysis and correlation studies.
"""
        
        zipf.writestr("README.txt", readme_content)
    
    print(f"✅ ZIP file created: {zip_filename}")
    print(f"📊 File size: {os.path.getsize(zip_filename) / (1024*1024):.2f} MB")
    
    return generated_images, zip_filename, generator.image_metadata

# ===== MAIN EXECUTION =====
if 'df_processed' in globals() and df_processed is not None:
    
    # Generate images
    num_images_to_generate = 15  # Adjust this number as needed
    images, zip_file, metadata = create_satellite_images_with_zip(df_processed, num_images_to_generate)
    
    # ===== DISPLAY IMAGES IN NOTEBOOK =====
    print(f"\n🖼️ DISPLAYING SAMPLE IMAGES IN NOTEBOOK")
    print("=" * 50)
    
    # Display first 6 images in a grid
    num_display = min(6, len(images))
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.flatten()
    
    for i in range(num_display):
        img, meta, filename = images[i]
        
        axes[i].imshow(img)
        axes[i].set_title(f"{meta['property_id']}\n"
                         f"Living Area: {meta['living_area']:,} sq ft\n"
                         f"Quality: {meta['overall_quality']}/10\n"
                         f"Year: {meta['year_built']}", fontsize=10)
        axes[i].axis('off')
    
    # Hide unused subplots
    for i in range(num_display, len(axes)):
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.suptitle('🛰️ Generated Satellite Images Sample', fontsize=16, y=1.02)
    plt.show()
    
    # ===== DISPLAY INDIVIDUAL DETAILED IMAGES =====
    print(f"\n🔍 DETAILED VIEW OF FIRST 3 PROPERTIES")
    print("-" * 40)
    
    for i in range(min(3, len(images))):
        img, meta, filename = images[i]
        
        print(f"\n📍 PROPERTY {i+1}: {meta['property_id']}")
        print(f"  • Living Area: {meta['living_area']:,} sq ft")
        print(f"  • Overall Quality: {meta['overall_quality']}/10")
        print(f"  • Year Built: {meta['year_built']}")
        print(f"  • Garage Cars: {meta['garage_cars']}")
        print(f"  • Lot Area: {meta['lot_area']:,} sq ft")
        print(f"  • Has Pool: {'Yes' if meta['has_pool'] else 'No'}")
        print(f"  • Trees: {meta['num_trees']}")
        print(f"  • File: {filename}")
        
        # Display large image
        plt.figure(figsize=(8, 8))
        plt.imshow(img)
        plt.title(f"🛰️ Satellite View - {meta['property_id']}\n"
                 f"Quality {meta['overall_quality']}/10 • {meta['living_area']:,} sq ft • Built {meta['year_built']}")
        plt.axis('off')
        plt.show()
    
    # ===== SUMMARY STATISTICS =====
    print(f"\n📊 GENERATION SUMMARY")
    print("=" * 40)
    print(f"✅ Total images generated: {len(images)}")
    print(f"✅ ZIP file created: {zip_file}")
    print(f"✅ Images directory: satellite_images/")
    print(f"✅ Metadata file: satellite_images/image_metadata.json")
    
    # Statistics from metadata
    qualities = [m['overall_quality'] for m in metadata]
    living_areas = [m['living_area'] for m in metadata]
    pools = sum(1 for m in metadata if m['has_pool'])
    
    print(f"\n📈 DATASET STATISTICS:")
    print(f"  • Average quality: {np.mean(qualities):.1f}/10")
    print(f"  • Average living area: {np.mean(living_areas):,.0f} sq ft")
    print(f"  • Properties with pools: {pools}/{len(metadata)} ({pools/len(metadata)*100:.1f}%)")
    print(f"  • Year range: {min(m['year_built'] for m in metadata)} - {max(m['year_built'] for m in metadata)}")
    
    # ===== DOWNLOAD INSTRUCTIONS =====
    print(f"\n💾 DOWNLOAD INSTRUCTIONS")
    print("=" * 40)
    print(f"1. 📁 ZIP file location: {zip_file}")
    print(f"2. 📂 Individual images: satellite_images/ folder")
    print(f"3. 📋 Metadata: satellite_images/image_metadata.json")
    print(f"4. 📖 Documentation: README.txt (inside ZIP)")
    
    print(f"\n🎯 TO ACCESS YOUR IMAGES:")
    print(f"  • Download the ZIP file: {zip_file}")
    print(f"  • Extract to view all {len(images)} satellite images")
    print(f"  • Each image is 512x512 pixels, PNG format")
    print(f"  • Use metadata.json for detailed property information")
    
    # Create download link (if in Jupyter)
    try:
        from IPython.display import FileLink
        print(f"\n⬇️ DIRECT DOWNLOAD LINK:")
        display(FileLink(zip_file))
    except:
        print(f"\n💡 To download: Right-click on {zip_file} in file browser")
    
    print(f"\n🎉 SATELLITE IMAGE GENERATION COMPLETE!")
    print("=" * 60)

else:
    print("❌ df_processed not available")
    print("Please ensure your house price data is loaded as 'df_processed'")
    print("\n💡 To use this generator:")
    print("1. Load your processed house price dataset")
    print("2. Ensure it has columns: GrLivArea, OverallQual, YearBuilt, etc.")
    print("3. Run this cell after data is available")
