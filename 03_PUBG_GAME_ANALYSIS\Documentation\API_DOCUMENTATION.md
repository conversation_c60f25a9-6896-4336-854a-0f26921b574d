# PUBG Game Analytics System - API Documentation

## Overview
The PUBG Game Analytics API provides programmatic access to player statistics, match data, predictions, and analytics insights.

## Base URL
```
https://api.pubganalytics.com/v1
```

## Authentication
All API requests require authentication using API keys.

```http
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

## Rate Limits
- **Free Tier**: 100 requests per hour
- **Pro Tier**: 1,000 requests per hour
- **Enterprise Tier**: 10,000 requests per hour

## Endpoints

### Player Analytics

#### Get Player Statistics
```http
GET /players/{player_id}/stats
```

**Parameters:**
- `player_id` (string): Unique player identifier
- `season` (string, optional): Game season filter
- `mode` (string, optional): Game mode filter

**Response:**
```json
{
  "player_id": "player123",
  "stats": {
    "matches_played": 150,
    "wins": 12,
    "kills": 450,
    "deaths": 138,
    "kd_ratio": 3.26,
    "avg_damage": 285.5,
    "win_rate": 0.08
  },
  "last_updated": "2024-12-01T10:30:00Z"
}
```

#### Get Player Performance Prediction
```http
POST /players/{player_id}/predict
```

**Request Body:**
```json
{
  "match_type": "squad",
  "map": "erangel",
  "team_composition": ["player1", "player2", "player3"]
}
```

**Response:**
```json
{
  "player_id": "player123",
  "predictions": {
    "win_probability": 0.75,
    "expected_kills": 3.2,
    "expected_damage": 420.5,
    "survival_probability": 0.85
  },
  "confidence_score": 0.92
}
```

### Match Analytics

#### Get Match Details
```http
GET /matches/{match_id}
```

**Response:**
```json
{
  "match_id": "match456",
  "game_mode": "squad",
  "map": "erangel",
  "duration": 1845,
  "players": [
    {
      "player_id": "player123",
      "placement": 1,
      "kills": 5,
      "damage": 650.2,
      "survival_time": 1845
    }
  ],
  "match_stats": {
    "total_players": 100,
    "total_teams": 25,
    "circle_count": 8
  }
}
```

#### Get Live Match Data
```http
GET /matches/{match_id}/live
```

**Response:**
```json
{
  "match_id": "match789",
  "status": "in_progress",
  "current_phase": "mid_game",
  "alive_players": 45,
  "circle_info": {
    "phase": 4,
    "center": {"x": 500, "y": 600},
    "radius": 150
  },
  "live_stats": {
    "total_kills": 55,
    "teams_remaining": 12
  }
}
```

### Team Analytics

#### Get Team Performance
```http
GET /teams/{team_id}/performance
```

**Response:**
```json
{
  "team_id": "team001",
  "members": ["player1", "player2", "player3", "player4"],
  "team_stats": {
    "matches_played": 50,
    "wins": 15,
    "win_rate": 0.30,
    "avg_placement": 8.5,
    "coordination_score": 0.85
  },
  "synergy_analysis": {
    "best_duo": ["player1", "player2"],
    "communication_score": 0.78,
    "role_distribution": {
      "fragger": "player1",
      "support": "player2",
      "igl": "player3",
      "sniper": "player4"
    }
  }
}
```

### Analytics and Insights

#### Get Player Recommendations
```http
GET /players/{player_id}/recommendations
```

**Response:**
```json
{
  "player_id": "player123",
  "recommendations": {
    "weapon_loadout": {
      "primary": "AKM",
      "secondary": "M416",
      "confidence": 0.88
    },
    "strategy_tips": [
      "Focus on early game positioning",
      "Improve close-range combat skills",
      "Practice vehicle rotations"
    ],
    "training_focus": {
      "aim_training": 0.7,
      "positioning": 0.9,
      "game_sense": 0.6
    }
  }
}
```

#### Get Market Intelligence
```http
GET /analytics/market-intelligence
```

**Response:**
```json
{
  "meta_analysis": {
    "popular_weapons": [
      {"weapon": "M416", "usage_rate": 0.65},
      {"weapon": "AKM", "usage_rate": 0.45}
    ],
    "map_preferences": [
      {"map": "erangel", "play_rate": 0.35},
      {"map": "sanhok", "play_rate": 0.25}
    ]
  },
  "trend_analysis": {
    "emerging_strategies": [
      "Early game aggression increase",
      "Vehicle rotation optimization"
    ],
    "declining_strategies": [
      "Passive camping strategies"
    ]
  }
}
```

## Error Handling

### Error Response Format
```json
{
  "error": {
    "code": "PLAYER_NOT_FOUND",
    "message": "Player with ID 'player123' not found",
    "details": {
      "timestamp": "2024-12-01T10:30:00Z",
      "request_id": "req_abc123"
    }
  }
}
```

### Common Error Codes
- `400 BAD_REQUEST`: Invalid request parameters
- `401 UNAUTHORIZED`: Invalid or missing API key
- `403 FORBIDDEN`: Insufficient permissions
- `404 NOT_FOUND`: Resource not found
- `429 RATE_LIMITED`: Rate limit exceeded
- `500 INTERNAL_ERROR`: Server error

## SDKs and Libraries

### Python SDK
```python
from pubg_analytics import PUBGAnalytics

client = PUBGAnalytics(api_key="your_api_key")
stats = client.get_player_stats("player123")
```

### JavaScript SDK
```javascript
const PUBGAnalytics = require('pubg-analytics');

const client = new PUBGAnalytics('your_api_key');
const stats = await client.getPlayerStats('player123');
```

## Webhooks

### Event Types
- `match.completed`: Match has finished
- `player.achievement`: Player achieved milestone
- `prediction.updated`: New prediction available

### Webhook Payload
```json
{
  "event_type": "match.completed",
  "timestamp": "2024-12-01T10:30:00Z",
  "data": {
    "match_id": "match456",
    "players": ["player1", "player2"],
    "winner": "player1"
  }
}
```

---
**API Version**: 1.0
**Last Updated**: December 2024
**Support**: <EMAIL>
