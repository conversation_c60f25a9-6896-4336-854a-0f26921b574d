# ===== FIXED KAFKA INSTALLATION & CONFIGURATION =====
# Complete solution for Kafka-Python integration

import subprocess
import sys
import os
import time
import json
import threading
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_kafka_python():
    """Install kafka-python with proper error handling"""
    try:
        print("🔧 Installing kafka-python...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "kafka-python==2.0.2", "--upgrade"
        ])
        print("✅ kafka-python installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install kafka-python: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error during installation: {e}")
        return False

def install_additional_dependencies():
    """Install additional Kafka-related dependencies"""
    dependencies = [
        "confluent-kafka==2.3.0",  # Alternative Kafka client
        "avro-python3==1.11.3",    # Avro serialization
        "fastavro==1.9.4",         # Fast Avro implementation
        "jsonschema==4.20.0"       # JSON schema validation
    ]
    
    for dep in dependencies:
        try:
            print(f"🔧 Installing {dep}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", dep
            ])
            print(f"✅ {dep} installed successfully!")
        except Exception as e:
            print(f"⚠️ Warning: Failed to install {dep}: {e}")

def check_kafka_installation():
    """Check if Kafka-Python is properly installed"""
    try:
        import kafka
        from kafka import KafkaProducer, KafkaConsumer
        from kafka.admin import KafkaAdminClient, NewTopic
        print("✅ kafka-python is properly installed and importable!")
        print(f"📦 Kafka-Python version: {kafka.__version__}")
        return True
    except ImportError as e:
        print(f"❌ kafka-python import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error checking Kafka: {e}")
        return False

def setup_local_kafka():
    """Setup instructions for local Kafka installation"""
    print("\n🐳 LOCAL KAFKA SETUP OPTIONS:")
    print("="*50)
    
    print("\n📋 OPTION 1: Docker Compose (Recommended)")
    print("Create docker-compose.yml:")
    docker_compose = """
version: '3.8'
services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    hostname: zookeeper
    container_name: zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    hostname: kafka
    container_name: kafka
    depends_on:
      - zookeeper
    ports:
      - "29092:29092"
      - "9092:9092"
      - "9101:9101"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_JMX_PORT: 9101
      KAFKA_JMX_HOSTNAME: localhost

  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui
    depends_on:
      - kafka
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
"""
    print(docker_compose)
    
    print("\n📋 OPTION 2: Manual Installation")
    print("1. Download Kafka from: https://kafka.apache.org/downloads")
    print("2. Extract and navigate to Kafka directory")
    print("3. Start Zookeeper: bin/zookeeper-server-start.sh config/zookeeper.properties")
    print("4. Start Kafka: bin/kafka-server-start.sh config/server.properties")
    
    print("\n📋 OPTION 3: Cloud Kafka Services")
    print("• Confluent Cloud: https://confluent.cloud/")
    print("• Amazon MSK: AWS Managed Streaming for Apache Kafka")
    print("• Azure Event Hubs: Kafka-compatible event streaming")
    print("• Google Cloud Pub/Sub: Alternative messaging service")

class EnhancedKafkaStreaming:
    """Enhanced Kafka streaming with fallback mechanisms"""
    
    def __init__(self, bootstrap_servers='localhost:9092', use_kafka=True):
        self.bootstrap_servers = bootstrap_servers
        self.use_kafka = use_kafka
        self.kafka_available = False
        self.producer = None
        self.consumer = None
        self.admin_client = None
        
        # Fallback in-memory queue
        self.fallback_queue = []
        self.fallback_lock = threading.Lock()
        
        # Initialize Kafka connection
        if self.use_kafka:
            self._initialize_kafka()
    
    def _initialize_kafka(self):
        """Initialize Kafka connection with error handling"""
        try:
            from kafka import KafkaProducer, KafkaConsumer
            from kafka.admin import KafkaAdminClient, NewTopic
            from kafka.errors import NoBrokersAvailable
            
            # Test connection
            test_producer = KafkaProducer(
                bootstrap_servers=self.bootstrap_servers,
                value_serializer=lambda v: json.dumps(v).encode('utf-8'),
                request_timeout_ms=5000,
                api_version=(0, 10, 1)
            )
            
            # If we get here, Kafka is available
            self.producer = test_producer
            self.kafka_available = True
            
            # Initialize consumer
            self.consumer = KafkaConsumer(
                bootstrap_servers=self.bootstrap_servers,
                value_deserializer=lambda m: json.loads(m.decode('utf-8')),
                auto_offset_reset='latest',
                api_version=(0, 10, 1)
            )
            
            # Initialize admin client
            self.admin_client = KafkaAdminClient(
                bootstrap_servers=self.bootstrap_servers,
                api_version=(0, 10, 1)
            )
            
            print("✅ Kafka connection established successfully!")
            
        except ImportError:
            print("⚠️ kafka-python not available, using fallback mode")
            self.kafka_available = False
        except NoBrokersAvailable:
            print("⚠️ Kafka brokers not available, using fallback mode")
            self.kafka_available = False
        except Exception as e:
            print(f"⚠️ Kafka initialization failed: {e}, using fallback mode")
            self.kafka_available = False
    
    def create_topic(self, topic_name, num_partitions=1, replication_factor=1):
        """Create Kafka topic with error handling"""
        if not self.kafka_available:
            print(f"📝 Topic '{topic_name}' created in fallback mode")
            return True
        
        try:
            from kafka.admin import NewTopic
            
            topic = NewTopic(
                name=topic_name,
                num_partitions=num_partitions,
                replication_factor=replication_factor
            )
            
            self.admin_client.create_topics([topic])
            print(f"✅ Kafka topic '{topic_name}' created successfully!")
            return True
            
        except Exception as e:
            print(f"⚠️ Failed to create topic '{topic_name}': {e}")
            return False
    
    def produce_message(self, topic, message):
        """Produce message to Kafka or fallback queue"""
        if self.kafka_available and self.producer:
            try:
                # Add timestamp to message
                enhanced_message = {
                    **message,
                    'timestamp': datetime.now().isoformat(),
                    'source': 'kafka_producer'
                }
                
                future = self.producer.send(topic, enhanced_message)
                record_metadata = future.get(timeout=10)
                
                print(f"📤 Message sent to Kafka topic '{topic}' "
                      f"(partition: {record_metadata.partition}, "
                      f"offset: {record_metadata.offset})")
                return True
                
            except Exception as e:
                print(f"❌ Failed to send message to Kafka: {e}")
                # Fallback to in-memory queue
                return self._fallback_produce(topic, message)
        else:
            return self._fallback_produce(topic, message)
    
    def _fallback_produce(self, topic, message):
        """Fallback message production using in-memory queue"""
        with self.fallback_lock:
            enhanced_message = {
                **message,
                'topic': topic,
                'timestamp': datetime.now().isoformat(),
                'source': 'fallback_queue'
            }
            self.fallback_queue.append(enhanced_message)
            print(f"📤 Message added to fallback queue for topic '{topic}'")
            return True
    
    def consume_messages(self, topic, callback_function, timeout_ms=1000):
        """Consume messages from Kafka or fallback queue"""
        if self.kafka_available and self.consumer:
            try:
                self.consumer.subscribe([topic])
                print(f"👂 Listening for messages on Kafka topic '{topic}'...")
                
                for message in self.consumer:
                    try:
                        callback_function(message.value)
                    except Exception as e:
                        print(f"❌ Error processing message: {e}")
                        
            except Exception as e:
                print(f"❌ Kafka consumer error: {e}")
                # Fallback to in-memory queue
                self._fallback_consume(topic, callback_function)
        else:
            self._fallback_consume(topic, callback_function)
    
    def _fallback_consume(self, topic, callback_function):
        """Fallback message consumption using in-memory queue"""
        print(f"👂 Listening for messages on fallback queue for topic '{topic}'...")
        
        while True:
            with self.fallback_lock:
                # Find messages for this topic
                topic_messages = [msg for msg in self.fallback_queue if msg.get('topic') == topic]
                
                for message in topic_messages:
                    try:
                        callback_function(message)
                        self.fallback_queue.remove(message)
                    except Exception as e:
                        print(f"❌ Error processing fallback message: {e}")
            
            time.sleep(1)  # Check every second
    
    def get_topic_info(self, topic):
        """Get topic information"""
        if self.kafka_available:
            try:
                metadata = self.admin_client.describe_topics([topic])
                return metadata
            except Exception as e:
                print(f"❌ Failed to get topic info: {e}")
                return None
        else:
            # Return fallback info
            with self.fallback_lock:
                topic_messages = [msg for msg in self.fallback_queue if msg.get('topic') == topic]
                return {
                    'topic': topic,
                    'message_count': len(topic_messages),
                    'mode': 'fallback_queue'
                }
    
    def close(self):
        """Close Kafka connections"""
        if self.producer:
            self.producer.close()
        if self.consumer:
            self.consumer.close()
        if self.admin_client:
            self.admin_client.close()

def main():
    """Main function to test Kafka installation and setup"""
    print("🚀 KAFKA-PYTHON INSTALLATION & SETUP")
    print("="*50)
    
    # Step 1: Install kafka-python
    if not check_kafka_installation():
        print("\n📦 Installing kafka-python...")
        if install_kafka_python():
            install_additional_dependencies()
            if not check_kafka_installation():
                print("❌ Installation failed. Please check your Python environment.")
                return False
        else:
            print("❌ Failed to install kafka-python. Using fallback mode.")
    
    # Step 2: Setup local Kafka (instructions)
    setup_local_kafka()
    
    # Step 3: Test Kafka streaming
    print("\n🧪 TESTING KAFKA STREAMING...")
    kafka_stream = EnhancedKafkaStreaming()
    
    # Create test topic
    kafka_stream.create_topic('house_price_predictions')
    
    # Test message production
    test_message = {
        'house_id': 'TEST_001',
        'predicted_price': 250000,
        'confidence': 0.95,
        'features': {
            'bedrooms': 3,
            'bathrooms': 2,
            'sqft': 1500
        }
    }
    
    kafka_stream.produce_message('house_price_predictions', test_message)
    
    # Test message consumption (callback function)
    def process_prediction(message):
        print(f"📨 Received prediction: {message}")
    
    # Note: In real implementation, this would run in a separate thread
    print("✅ Kafka streaming test completed!")
    
    kafka_stream.close()
    return True

if __name__ == "__main__":
    main()
