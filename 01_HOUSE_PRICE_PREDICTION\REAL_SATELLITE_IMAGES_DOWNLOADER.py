# ===== REAL SATELLITE IMAGES DOWNLOADER WITH ZIP =====
# Based on Section 45 - Downloads REAL satellite images from free APIs

import requests
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from PIL import Image
import os
import zipfile
import json
import math
import time
import random
from datetime import datetime
import io
from IPython.display import display, HTML, FileLink
import warnings
warnings.filterwarnings('ignore')

print("🛰️ REAL SATELLITE IMAGES DOWNLOADER")
print("=" * 60)

class RealSatelliteDownloader:
    """Download real satellite images from free APIs"""
    
    def __init__(self):
        self.images_downloaded = 0
        self.successful_downloads = []
        self.failed_downloads = []
        self.image_metadata = []
        
    def deg2num(self, lat_deg, lon_deg, zoom):
        """Convert lat/lon to tile coordinates"""
        lat_rad = math.radians(lat_deg)
        n = 2.0 ** zoom
        x = int((lon_deg + 180.0) / 360.0 * n)
        y = int((1.0 - math.asinh(math.tan(lat_rad)) / math.pi) / 2.0 * n)
        return (x, y)
    
    def get_esri_world_imagery(self, lat, lon, zoom=18):
        """Get satellite imagery from ESRI World Imagery - FREE, HIGH QUALITY"""
        x, y = self.deg2num(lat, lon, zoom)
        url = f"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{zoom}/{y}/{x}"
        
        try:
            response = requests.get(url, timeout=15, headers={
                'User-Agent': 'House Price Prediction Research Project'
            })
            if response.status_code == 200:
                return Image.open(io.BytesIO(response.content)), "ESRI World Imagery"
        except Exception as e:
            print(f"ESRI error: {e}")
        return None, None
    
    def get_usgs_satellite(self, lat, lon, zoom=18):
        """Get satellite imagery from USGS National Map - FREE, US ONLY"""
        x, y = self.deg2num(lat, lon, zoom)
        url = f"https://basemap.nationalmap.gov/arcgis/rest/services/USGSImageryOnly/MapServer/tile/{zoom}/{y}/{x}"
        
        try:
            response = requests.get(url, timeout=15)
            if response.status_code == 200:
                return Image.open(io.BytesIO(response.content)), "USGS Satellite"
        except Exception as e:
            print(f"USGS error: {e}")
        return None, None
    
    def get_openstreetmap_tile(self, lat, lon, zoom=18):
        """Get map tile from OpenStreetMap - FREE, GLOBAL"""
        x, y = self.deg2num(lat, lon, zoom)
        url = f"https://tile.openstreetmap.org/{zoom}/{x}/{y}.png"
        
        try:
            response = requests.get(url, timeout=15, headers={
                'User-Agent': 'House Price Prediction Research Project'
            })
            if response.status_code == 200:
                return Image.open(io.BytesIO(response.content)), "OpenStreetMap"
        except Exception as e:
            print(f"OpenStreetMap error: {e}")
        return None, None
    
    def get_satellite_image_multi_source(self, lat, lon, zoom=18):
        """Try multiple free sources with fallback"""
        sources = [
            ("ESRI World Imagery", self.get_esri_world_imagery),
            ("USGS Satellite", self.get_usgs_satellite),
            ("OpenStreetMap", self.get_openstreetmap_tile)
        ]
        
        for source_name, source_func in sources:
            try:
                image, source = source_func(lat, lon, zoom)
                if image is not None:
                    return image, source_name
            except Exception as e:
                print(f"Error with {source_name}: {e}")
                continue
        
        return None, None
    
    def extract_features_from_real_image(self, image):
        """Extract computer vision features from real satellite image"""
        if image is None:
            return {}
        
        # Convert to numpy array
        img_array = np.array(image.convert('RGB'))
        
        # Convert to HSV for better analysis
        img_hsv = np.array(image.convert('HSV'))
        
        features = {}
        
        # Basic color analysis
        features['avg_red'] = float(np.mean(img_array[:, :, 0]))
        features['avg_green'] = float(np.mean(img_array[:, :, 1]))
        features['avg_blue'] = float(np.mean(img_array[:, :, 2]))
        
        # Green space detection (vegetation)
        green_mask = (
            (img_hsv[:, :, 0] > 60) & (img_hsv[:, :, 0] < 180) &  # Hue: green range
            (img_hsv[:, :, 1] > 50) &  # Saturation: not too gray
            (img_hsv[:, :, 2] > 50)    # Value: not too dark
        )
        features['green_space_ratio'] = float(np.sum(green_mask) / green_mask.size)
        
        # Built area detection (gray/brown areas)
        built_mask = (
            (img_hsv[:, :, 1] < 50) |  # Low saturation (gray)
            ((img_hsv[:, :, 0] > 10) & (img_hsv[:, :, 0] < 30))  # Brown hues
        )
        features['built_area_ratio'] = float(np.sum(built_mask) / built_mask.size)
        
        # Water detection (blue areas)
        water_mask = (
            (img_hsv[:, :, 0] > 200) & (img_hsv[:, :, 0] < 240) &  # Blue hue
            (img_hsv[:, :, 1] > 100) &  # High saturation
            (img_hsv[:, :, 2] > 100)    # Bright
        )
        features['water_ratio'] = float(np.sum(water_mask) / water_mask.size)
        
        # Image quality metrics
        features['brightness'] = float(np.mean(img_array))
        features['contrast'] = float(np.std(img_array))
        
        # Texture analysis (simplified)
        gray = np.array(image.convert('L'))
        features['texture_variance'] = float(np.var(gray))
        
        return features

def generate_realistic_coordinates(num_locations=20):
    """Generate realistic US coordinates for satellite image download"""
    
    # Major US metropolitan areas with realistic coordinates
    metro_areas = [
        {"name": "New York", "lat": 40.7128, "lon": -74.0060, "radius": 0.5},
        {"name": "Los Angeles", "lat": 34.0522, "lon": -118.2437, "radius": 0.4},
        {"name": "Chicago", "lat": 41.8781, "lon": -87.6298, "radius": 0.3},
        {"name": "Houston", "lat": 29.7604, "lon": -95.3698, "radius": 0.3},
        {"name": "Phoenix", "lat": 33.4484, "lon": -112.0740, "radius": 0.3},
        {"name": "Philadelphia", "lat": 39.9526, "lon": -75.1652, "radius": 0.2},
        {"name": "San Antonio", "lat": 29.4241, "lon": -98.4936, "radius": 0.2},
        {"name": "San Diego", "lat": 32.7157, "lon": -117.1611, "radius": 0.2},
        {"name": "Dallas", "lat": 32.7767, "lon": -96.7970, "radius": 0.3},
        {"name": "San Jose", "lat": 37.3382, "lon": -121.8863, "radius": 0.2},
        {"name": "Austin", "lat": 30.2672, "lon": -97.7431, "radius": 0.2},
        {"name": "Jacksonville", "lat": 30.3322, "lon": -81.6557, "radius": 0.2},
        {"name": "Fort Worth", "lat": 32.7555, "lon": -97.3308, "radius": 0.2},
        {"name": "Columbus", "lat": 39.9612, "lon": -82.9988, "radius": 0.2},
        {"name": "Charlotte", "lat": 35.2271, "lon": -80.8431, "radius": 0.2},
        {"name": "Seattle", "lat": 47.6062, "lon": -122.3321, "radius": 0.2},
        {"name": "Denver", "lat": 39.7392, "lon": -104.9903, "radius": 0.2},
        {"name": "Boston", "lat": 42.3601, "lon": -71.0589, "radius": 0.2},
        {"name": "Nashville", "lat": 36.1627, "lon": -86.7816, "radius": 0.2},
        {"name": "Miami", "lat": 25.7617, "lon": -80.1918, "radius": 0.2}
    ]
    
    locations = []
    
    for i in range(num_locations):
        # Select random metro area
        metro = random.choice(metro_areas)
        
        # Generate random coordinates within the metro area
        lat_offset = random.uniform(-metro["radius"], metro["radius"])
        lon_offset = random.uniform(-metro["radius"], metro["radius"])
        
        location = {
            "property_id": f"REAL_PROP_{i+1:03d}",
            "name": f"{metro['name']} Area Property {i+1}",
            "lat": metro["lat"] + lat_offset,
            "lon": metro["lon"] + lon_offset,
            "metro_area": metro["name"]
        }
        
        locations.append(location)
    
    return locations

def download_real_satellite_images(num_images=15):
    """Download real satellite images and create ZIP file"""
    
    print(f"🎯 DOWNLOADING {num_images} REAL SATELLITE IMAGES")
    print("-" * 50)
    
    # Create directories
    images_dir = "real_satellite_images"
    if not os.path.exists(images_dir):
        os.makedirs(images_dir)
    
    # Initialize downloader
    downloader = RealSatelliteDownloader()
    
    # Generate realistic coordinates
    locations = generate_realistic_coordinates(num_images)
    
    downloaded_images = []
    
    print(f"📡 Downloading real satellite images...")
    
    for i, location in enumerate(locations):
        print(f"\n📍 Location {i+1}/{num_images}: {location['name']}")
        print(f"   Coordinates: {location['lat']:.4f}, {location['lon']:.4f}")
        
        # Download satellite image
        image, source = downloader.get_satellite_image_multi_source(
            location['lat'], location['lon'], zoom=18
        )
        
        if image is not None:
            # Extract features
            features = downloader.extract_features_from_real_image(image)
            
            # Create metadata
            metadata = {
                **location,
                'source': source,
                'download_time': datetime.now().isoformat(),
                'zoom_level': 18,
                'image_size': image.size,
                **features
            }
            
            # Save image
            image_filename = f"{images_dir}/{location['property_id']}_satellite.png"
            image.save(image_filename, 'PNG', quality=95)
            
            # Store for display
            downloaded_images.append((image, metadata, image_filename))
            downloader.successful_downloads.append(location['property_id'])
            downloader.image_metadata.append(metadata)
            
            print(f"   ✅ Downloaded from {source}")
            print(f"   📊 Green space: {features['green_space_ratio']:.3f}")
            print(f"   🏗️ Built area: {features['built_area_ratio']:.3f}")
            print(f"   💧 Water: {features['water_ratio']:.3f}")
            
        else:
            print(f"   ❌ Failed to download")
            downloader.failed_downloads.append(location['property_id'])
        
        # Add delay to be respectful to APIs
        time.sleep(1)
    
    print(f"\n✅ Download complete!")
    print(f"   • Successful: {len(downloader.successful_downloads)}")
    print(f"   • Failed: {len(downloader.failed_downloads)}")
    
    # Save metadata
    metadata_file = f"{images_dir}/real_satellite_metadata.json"
    with open(metadata_file, 'w') as f:
        json.dump(downloader.image_metadata, f, indent=2)
    
    # Create ZIP file
    zip_filename = f"real_satellite_images_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
    
    print(f"\n📦 CREATING ZIP FILE: {zip_filename}")
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # Add all images
        for _, metadata, image_file in downloaded_images:
            zipf.write(image_file, os.path.basename(image_file))
        
        # Add metadata
        zipf.write(metadata_file, "real_satellite_metadata.json")
        
        # Add README
        readme_content = f"""# Real Satellite Images Dataset
Downloaded on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Total images: {len(downloaded_images)}
Successful downloads: {len(downloader.successful_downloads)}
Failed downloads: {len(downloader.failed_downloads)}

## Sources Used:
- ESRI World Imagery (High quality, global coverage)
- USGS Satellite Imagery (US only, very high quality)
- OpenStreetMap (Global coverage, map style)

## Files:
- REAL_PROP_XXX_satellite.png: Real satellite images
- real_satellite_metadata.json: Detailed metadata and features

## Metadata includes:
- Property coordinates (lat/lon)
- Download source and timestamp
- Computer vision features:
  * Green space ratio (vegetation)
  * Built area ratio (buildings/roads)
  * Water ratio (pools/lakes)
  * Image quality metrics

## Usage:
Extract the ZIP file and view real satellite images.
Use metadata.json for analysis and ML feature engineering.
All images are 256x256 pixels, PNG format.

## API Sources:
All images downloaded from free, public APIs:
- No API keys required
- No payment needed
- Respectful rate limiting applied
"""
        
        zipf.writestr("README.txt", readme_content)
    
    print(f"✅ ZIP file created: {zip_filename}")
    print(f"📊 File size: {os.path.getsize(zip_filename) / (1024*1024):.2f} MB")
    
    return downloaded_images, zip_filename, downloader.image_metadata

# ===== MAIN EXECUTION =====
print(f"\n🚀 STARTING REAL SATELLITE IMAGE DOWNLOAD")
print("=" * 60)

# Download real satellite images
num_images_to_download = 12  # Adjust this number as needed
images, zip_file, metadata = download_real_satellite_images(num_images_to_download)

if images:
    # ===== DISPLAY IMAGES IN NOTEBOOK =====
    print(f"\n🖼️ DISPLAYING REAL SATELLITE IMAGES")
    print("=" * 50)
    
    # Display first 6 images in a grid
    num_display = min(6, len(images))
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.flatten()
    
    for i in range(num_display):
        img, meta, filename = images[i]
        
        axes[i].imshow(img)
        axes[i].set_title(f"{meta['property_id']}\n"
                         f"{meta['metro_area']}\n"
                         f"Source: {meta['source']}\n"
                         f"Green: {meta['green_space_ratio']:.2f}", fontsize=9)
        axes[i].axis('off')
    
    # Hide unused subplots
    for i in range(num_display, len(axes)):
        axes[i].axis('off')
    
    plt.tight_layout()
    plt.suptitle('🛰️ Real Satellite Images from Free APIs', fontsize=16, y=1.02)
    plt.show()
    
    # ===== DISPLAY DETAILED INDIVIDUAL IMAGES =====
    print(f"\n🔍 DETAILED VIEW OF FIRST 3 REAL SATELLITE IMAGES")
    print("-" * 50)
    
    for i in range(min(3, len(images))):
        img, meta, filename = images[i]
        
        print(f"\n📍 PROPERTY {i+1}: {meta['property_id']}")
        print(f"  • Location: {meta['name']}")
        print(f"  • Coordinates: {meta['lat']:.4f}, {meta['lon']:.4f}")
        print(f"  • Source: {meta['source']}")
        print(f"  • Green space ratio: {meta['green_space_ratio']:.3f}")
        print(f"  • Built area ratio: {meta['built_area_ratio']:.3f}")
        print(f"  • Water ratio: {meta['water_ratio']:.3f}")
        print(f"  • Image brightness: {meta['brightness']:.1f}")
        print(f"  • Image contrast: {meta['contrast']:.1f}")
        print(f"  • File: {filename}")
        
        # Display large image
        plt.figure(figsize=(8, 8))
        plt.imshow(img)
        plt.title(f"🛰️ Real Satellite Image - {meta['property_id']}\n"
                 f"{meta['metro_area']} • Source: {meta['source']}\n"
                 f"Green: {meta['green_space_ratio']:.2f} • Built: {meta['built_area_ratio']:.2f}")
        plt.axis('off')
        plt.show()
    
    # ===== ENHANCED DOWNLOAD OPTIONS =====
    print(f"\n💾 DOWNLOAD YOUR REAL SATELLITE IMAGES")
    print("=" * 50)
    
    # Multiple download methods
    try:
        from IPython.display import FileLink, HTML
        
        print(f"⬇️ DOWNLOAD OPTIONS:")
        
        # Method 1: Direct download link
        print("Option 1: Direct download link")
        display(FileLink(zip_file))
        
        # Method 2: HTML download button
        print("\nOption 2: Download button")
        download_html = f'''
        <div style="padding: 15px; border: 2px solid #2196F3; border-radius: 8px; background-color: #f0f8ff;">
            <h3>🛰️ Download Real Satellite Images</h3>
            <p><strong>File:</strong> {zip_file}</p>
            <p><strong>Size:</strong> {os.path.getsize(zip_file) / (1024*1024):.2f} MB</p>
            <p><strong>Images:</strong> {len(images)} real satellite images</p>
            <p><strong>Sources:</strong> ESRI, USGS, OpenStreetMap</p>
            <a href="{zip_file}" download="{zip_file}" 
               style="background-color: #2196F3; color: white; padding: 12px 24px; 
                      text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 16px;">
               🔽 Download Real Satellite Images ZIP
            </a>
        </div>
        '''
        display(HTML(download_html))
        
    except Exception as e:
        print(f"⚠️ Download link creation failed: {e}")
    
    # ===== SUMMARY STATISTICS =====
    print(f"\n📊 REAL SATELLITE IMAGES SUMMARY")
    print("=" * 50)
    print(f"✅ Total images downloaded: {len(images)}")
    print(f"✅ ZIP file created: {zip_file}")
    print(f"✅ Images directory: real_satellite_images/")
    print(f"✅ Metadata file: real_satellite_images/real_satellite_metadata.json")
    
    # Statistics from metadata
    sources = [m['source'] for m in metadata]
    green_ratios = [m['green_space_ratio'] for m in metadata]
    built_ratios = [m['built_area_ratio'] for m in metadata]
    
    print(f"\n📈 DATASET STATISTICS:")
    print(f"  • Sources used: {set(sources)}")
    print(f"  • Average green space: {np.mean(green_ratios):.3f}")
    print(f"  • Average built area: {np.mean(built_ratios):.3f}")
    print(f"  • Metro areas covered: {len(set(m['metro_area'] for m in metadata))}")
    
    print(f"\n🎉 REAL SATELLITE IMAGE DOWNLOAD COMPLETE!")
    print("=" * 60)
    print(f"🌟 You now have {len(images)} REAL satellite images from free APIs!")
    print(f"🌟 Perfect for computer vision and ML feature extraction!")
    print(f"🌟 No API keys required - completely free!")

else:
    print("❌ No images were successfully downloaded")
    print("This might be due to network issues or API limitations")
    print("Please check your internet connection and try again")
