{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Core Data Science Libraries\n", "import pandas as pd\n", "import numpy as np\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Statistical Analysis\n", "from scipy import stats\n", "from scipy.stats import normaltest, skew, kurtosis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Static Visualization\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interactive Visualization\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "import plotly.io as pio"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# System libraries\n", "import os\n", "import time\n", "import datetime"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Setup Visualization Environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure matplotlib for <PERSON><PERSON><PERSON>\n", "%matplotlib inline\n", "print(\"✓ Matplotlib inline mode activated\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set matplotlib style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "print(\"✓ Matplotlib style configured\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configure Plotly for Jupy<PERSON> (try multiple renderers)\n", "renderers_to_try = [\n", "    'notebook_connected',  # Modern Jupyter notebooks\n", "    'iframe',              # Fallback for most environments\n", "    'jupyterlab',          # JupyterLab specific\n", "    'colab',               # Google Colab\n", "    'browser'              # Opens in browser\n", "]\n", "\n", "for renderer in renderers_to_try:\n", "    try:\n", "        pio.renderers.default = renderer\n", "        print(f\"✓ Plotly renderer set to: {renderer}\")\n", "        break\n", "    except:\n", "        continue\n", "else:\n", "    print(\"⚠ Could not set <PERSON>lot<PERSON> renderer\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set pandas display options\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.precision', 3)\n", "print(\"✓ Pandas display options configured\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Diagnostic Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def diagnose_visualization_issues():\n", "    \"\"\"Diagnose common visualization issues in Jupyter notebooks.\"\"\"\n", "    print(\"VISUALIZATION DIAGNOSTICS\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Check if we're in Jupyter\n", "    try:\n", "        from IPython import get_ipython\n", "        ipython = get_ipython()\n", "        if i<PERSON>th<PERSON> is not None:\n", "            print(\"✓ Running in Jupyter environment\")\n", "        else:\n", "            print(\"⚠ Not running in Jupyter environment\")\n", "    except ImportError:\n", "        print(\"⚠ IPython not available\")\n", "    \n", "    return True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run diagnostics\n", "diagnose_visualization_issues()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check backends and versions\n", "print(f\"Matplotlib backend: {plt.get_backend()}\")\n", "print(f\"Plotly default renderer: {pio.renderers.default}\")\n", "print(f\"Matplotlib version: {plt.__version__}\")\n", "print(f\"Plotly version: {pio.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON> Matplotlib Plotting"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create sample data for matplotlib test\n", "x = np.linspace(0, 10, 100)\n", "y = np.sin(x)\n", "print(\"Sample data created for matplotlib test\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create matplotlib test plot\n", "fig, ax = plt.subplots(figsize=(8, 5))\n", "ax.plot(x, y, label='sin(x)', linewidth=2)\n", "ax.set_title('Matplotlib Test Plot', fontsize=14, fontweight='bold')\n", "ax.set_xlabel('X values')\n", "ax.set_ylabel('Y values')\n", "ax.legend()\n", "ax.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✓ Matplotlib test completed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON> Plotly <PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create Plotly test plot\n", "fig = go.Figure()\n", "fig.add_trace(go.<PERSON>(\n", "    x=x, \n", "    y=y, \n", "    mode='lines',\n", "    name='sin(x)',\n", "    line=dict(color='blue', width=2)\n", "))\n", "\n", "print(\"Plotly figure created\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Update Plotly layout\n", "fig.update_layout(\n", "    title='Plotly Test Plot',\n", "    xaxis_title='X values',\n", "    yaxis_title='Y values',\n", "    template='plotly_white',\n", "    width=800,\n", "    height=500\n", ")\n", "\n", "print(\"Layout updated\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display Plotly plot\n", "fig.show()\n", "print(\"✓ Plotly test completed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON> Sample PUBG Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate sample PUBG data\n", "np.random.seed(42)\n", "n_samples = 1000\n", "\n", "sample_data = pd.DataFrame({\n", "    'kills': np.random.poisson(2, n_samples),\n", "    'damageDealt': np.random.normal(500, 200, n_samples),\n", "    'walkDistance': np.random.normal(1000, 300, n_samples),\n", "    'winPlacePerc': np.random.beta(2, 5, n_samples)\n", "})\n", "\n", "print(f\"Sample data created: {sample_data.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Clean sample data (ensure non-negative values)\n", "sample_data['damageDealt'] = np.maximum(sample_data['damageDealt'], 0)\n", "sample_data['walkDistance'] = np.maximum(sample_data['walkDistance'], 0)\n", "\n", "print(\"Data cleaned:\")\n", "print(sample_data.describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON><PERSON> PUBG Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create interactive scatter plot\n", "pubg_fig = go.Figure()\n", "\n", "pubg_fig.add_trace(go.<PERSON>(\n", "    x=sample_data['walkDistance'],\n", "    y=sample_data['damageDealt'],\n", "    mode='markers',\n", "    marker=dict(\n", "        size=(sample_data['kills'] + 1) * 8,\n", "        color=sample_data['winPlacePerc'],\n", "        colorscale='Viridis',\n", "        opacity=0.7,\n", "        line=dict(width=1, color='black')\n", "    ),\n", "    name='Players'\n", "))\n", "\n", "print(\"PUBG scatter plot created\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add hover information\n", "pubg_fig.update_traces(\n", "    hovertemplate='<b>Walk Distance</b>: %{x:.0f}m<br>' +\n", "                 '<b>Damage Dealt</b>: %{y:.0f}<br>' +\n", "                 '<b>Kills</b>: %{customdata[0]}<br>' +\n", "                 '<b>Win Placement</b>: %{customdata[1]:.3f}<extra></extra>',\n", "    customdata=np.column_stack((sample_data['kills'], sample_data['winPlacePerc']))\n", ")\n", "\n", "print(\"Hover information added\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Update layout\n", "pubg_fig.update_layout(\n", "    title={\n", "        'text': 'Sample PUBG Player Performance Analysis<br><sub>Bubble size = Kills | Color = Win Placement</sub>',\n", "        'x': 0.5,\n", "        'xanchor': 'center',\n", "        'font': {'size': 16, 'family': 'Arial Black'}\n", "    },\n", "    xaxis_title='Walk Distance (meters)',\n", "    yaxis_title='Damage Dealt',\n", "    width=900,\n", "    height=600,\n", "    template='plotly_white'\n", ")\n", "\n", "print(\"Layout configured\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add colorbar\n", "pubg_fig.update_traces(\n", "    marker_colorbar=dict(\n", "        title=\"Win Placement Percentile\",\n", "        titleside=\"right\"\n", "    )\n", ")\n", "\n", "print(\"Colorbar added\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display the PUBG visualization\n", "pubg_fig.show()\n", "print(\"✓ Sample PUBG visualization displayed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Test Multiple Plotly <PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test different renderers if plots don't show\n", "print(\"Available Plotly renderers:\")\n", "for renderer in pio.renderers:\n", "    print(f\"  - {renderer}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"VISUALIZATION SETUP COMPLETE!\")\n", "print(\"=\" * 40)\n", "print(\"✓ Matplotlib configured and tested\")\n", "print(\"✓ Plotly configured and tested\")\n", "print(\"✓ Sample PUBG visualization created\")\n", "print(\"\\nIf plots are not showing:\")\n", "print(\"1. Try: pio.renderers.default = 'iframe'\")\n", "print(\"2. Try: pio.renderers.default = 'jupyterlab'\")\n", "print(\"3. Try: pio.renderers.default = 'colab'\")\n", "print(\"4. <PERSON>art kernel and re-run cells\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}