# Core libraries
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Visualization libraries
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.io as pio

# System libraries
import os
import gc
import time

# CRITICAL: Configure visualization backends
%matplotlib inline

# Set matplotlib style (like reference notebook)
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

# Configure plotly (modern approach)
pio.renderers.default = 'notebook_connected'

# Set pandas options
pd.options.display.float_format = '{:,.3f}'.format
pd.set_option('display.max_columns', None)

print("✅ Visualization environment configured successfully!")
print(f"📊 Matplotlib backend: {plt.get_backend()}")
print(f"🎨 Plotly renderer: {pio.renderers.default}")

# Define data path
DATA_PATH = '../data/pubg.csv'

# Check file existence and size
if os.path.exists(DATA_PATH):
    file_size = os.path.getsize(DATA_PATH)
    print(f"📁 File found: {DATA_PATH}")
    print(f"📊 Size: {file_size:,} bytes ({file_size/1024**2:.2f} MB)")
    
    if file_size > 100 * 1024 * 1024:  # > 100 MB
        print("⚠️  Large file detected - using optimized loading")
        USE_SAMPLE = True
    else:
        USE_SAMPLE = False
else:
    print(f"❌ File not found: {DATA_PATH}")
    USE_SAMPLE = True  # Will create synthetic data

# Load PUBG data efficiently
def load_pubg_data(file_path, sample_size=50000):
    """
    Load PUBG data with memory optimization.
    """
    print(f"📊 Loading PUBG data (sample size: {sample_size:,})...")
    
    try:
        # Load sample with optimized data types
        df = pd.read_csv(file_path, nrows=sample_size, low_memory=False)
        
        print(f"✅ Data loaded successfully: {df.shape}")
        print(f"💾 Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
        
        return df
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        print("📝 Creating synthetic PUBG data for testing...")
        
        # Create synthetic data
        np.random.seed(42)
        n_samples = min(sample_size, 10000)
        
        synthetic_data = pd.DataFrame({
            'kills': np.random.poisson(2, n_samples),
            'assists': np.random.poisson(1, n_samples),
            'boosts': np.random.poisson(3, n_samples),
            'heals': np.random.poisson(2, n_samples),
            'damageDealt': np.random.normal(500, 200, n_samples),
            'walkDistance': np.random.normal(1000, 300, n_samples),
            'winPlacePerc': np.random.beta(2, 5, n_samples)
        })
        
        # Clean synthetic data
        synthetic_data['damageDealt'] = np.maximum(synthetic_data['damageDealt'], 0)
        synthetic_data['walkDistance'] = np.maximum(synthetic_data['walkDistance'], 0)
        
        print(f"✅ Synthetic data created: {synthetic_data.shape}")
        return synthetic_data

# Load the data
if USE_SAMPLE:
    pubg_data = load_pubg_data(DATA_PATH, sample_size=50000)
else:
    try:
        pubg_data = pd.read_csv(DATA_PATH, low_memory=False)
        print(f"✅ Full dataset loaded: {pubg_data.shape}")
    except:
        pubg_data = load_pubg_data(DATA_PATH, sample_size=50000)

# Display basic information about the dataset
print("📊 PUBG DATASET OVERVIEW")
print("=" * 40)
print(f"Shape: {pubg_data.shape}")
print(f"Memory usage: {pubg_data.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
print(f"Missing values: {pubg_data.isnull().sum().sum()}")

print(f"\n📋 Columns ({len(pubg_data.columns)}):")
for i, col in enumerate(pubg_data.columns, 1):
    print(f"  {i:2d}. {col}")

# Display first few rows
print(f"\n📈 First 5 rows:")
display(pubg_data.head())

# Basic statistics for numerical columns
numerical_cols = pubg_data.select_dtypes(include=[np.number]).columns
print(f"📊 Basic statistics for numerical columns:")
display(pubg_data[numerical_cols].describe())

# Test 1: Simple matplotlib plot
print("🧪 Testing matplotlib basic functionality...")

# Create a simple test plot
fig, ax = plt.subplots(figsize=(8, 5))
x = np.linspace(0, 10, 100)
y = np.sin(x)
ax.plot(x, y, label='sin(x)', linewidth=2)
ax.set_title('Matplotlib Test - Sine Wave', fontsize=14, fontweight='bold')
ax.set_xlabel('X values')
ax.set_ylabel('Y values')
ax.legend()
ax.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print("✅ Matplotlib test completed - if you see the plot above, matplotlib is working!")

# Test 2: PUBG data with matplotlib
if 'kills' in pubg_data.columns:
    print("🎮 Creating PUBG matplotlib visualizations...")
    
    # Create subplots for multiple visualizations
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('PUBG Player Statistics - Matplotlib Visualizations', fontsize=16, fontweight='bold')
    
    # 1. Kills distribution
    axes[0, 0].hist(pubg_data['kills'], bins=range(0, min(pubg_data['kills'].max() + 2, 21)), 
                   alpha=0.7, edgecolor='black', color='skyblue')
    axes[0, 0].set_title('Distribution of Kills per Match')
    axes[0, 0].set_xlabel('Number of Kills')
    axes[0, 0].set_ylabel('Frequency')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. Damage dealt distribution
    if 'damageDealt' in pubg_data.columns:
        axes[0, 1].hist(pubg_data['damageDealt'], bins=50, alpha=0.7, 
                       edgecolor='black', color='lightcoral')
        axes[0, 1].set_title('Distribution of Damage Dealt')
        axes[0, 1].set_xlabel('Damage Dealt')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].grid(True, alpha=0.3)
    
    # 3. Walk distance vs Kills scatter
    if 'walkDistance' in pubg_data.columns:
        scatter = axes[1, 0].scatter(pubg_data['walkDistance'], pubg_data['kills'], 
                                   alpha=0.6, c=pubg_data['kills'], cmap='viridis')
        axes[1, 0].set_title('Walk Distance vs Kills')
        axes[1, 0].set_xlabel('Walk Distance (meters)')
        axes[1, 0].set_ylabel('Number of Kills')
        axes[1, 0].grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=axes[1, 0], label='Kills')
    
    # 4. Win placement distribution
    if 'winPlacePerc' in pubg_data.columns:
        axes[1, 1].hist(pubg_data['winPlacePerc'], bins=50, alpha=0.7, 
                       edgecolor='black', color='lightgreen')
        axes[1, 1].set_title('Win Placement Percentile Distribution')
        axes[1, 1].set_xlabel('Win Placement Percentile')
        axes[1, 1].set_ylabel('Frequency')
        axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    print("✅ PUBG matplotlib visualizations completed!")
else:
    print("⚠️ 'kills' column not found - using available numerical columns")
    
    # Plot first few numerical columns
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    if len(numerical_cols) >= 2:
        axes[0].hist(pubg_data[numerical_cols[0]], bins=30, alpha=0.7, edgecolor='black')
        axes[0].set_title(f'Distribution of {numerical_cols[0]}')
        axes[0].grid(True, alpha=0.3)
        
        axes[1].hist(pubg_data[numerical_cols[1]], bins=30, alpha=0.7, edgecolor='black')
        axes[1].set_title(f'Distribution of {numerical_cols[1]}')
        axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()

# Test 1: Simple plotly plot
print("🧪 Testing Plotly basic functionality...")

# Create a simple test plot
x = np.linspace(0, 10, 100)
y = np.sin(x)

fig = go.Figure()
fig.add_trace(go.Scatter(
    x=x, 
    y=y, 
    mode='lines',
    name='sin(x)',
    line=dict(color='blue', width=2)
))

fig.update_layout(
    title='Plotly Test - Sine Wave',
    xaxis_title='X values',
    yaxis_title='Y values',
    template='plotly_white',
    width=800,
    height=500
)

fig.show()
print("✅ Plotly test completed - if you see the interactive plot above, Plotly is working!")

# Test 2: PUBG data with Plotly (Interactive)
if 'kills' in pubg_data.columns and 'damageDealt' in pubg_data.columns:
    print("🎮 Creating PUBG Plotly visualizations...")
    
    # Interactive scatter plot
    x_col = 'walkDistance' if 'walkDistance' in pubg_data.columns else 'damageDealt'
    y_col = 'damageDealt' if 'damageDealt' in pubg_data.columns else pubg_data.columns[1]
    color_col = 'winPlacePerc' if 'winPlacePerc' in pubg_data.columns else 'kills'
    
    fig = go.Figure()
    
    fig.add_trace(go.Scatter(
        x=pubg_data[x_col],
        y=pubg_data[y_col],
        mode='markers',
        marker=dict(
            size=(pubg_data['kills'] + 1) * 4,  # Bubble size based on kills
            color=pubg_data[color_col],
            colorscale='Viridis',
            opacity=0.6,
            line=dict(width=0.5, color='black'),
            colorbar=dict(
                title=color_col.replace('Perc', ' Percentile').title(),
                titleside="right"
            )
        ),
        hovertemplate=f'<b>{x_col.title()}</b>: %{{x:.0f}}<br>' +
                     f'<b>{y_col.title()}</b>: %{{y:.0f}}<br>' +
                     f'<b>Kills</b>: %{{customdata[0]}}<br>' +
                     f'<b>{color_col.title()}</b>: %{{customdata[1]:.3f}}<extra></extra>',
        customdata=np.column_stack((pubg_data['kills'], pubg_data[color_col])),
        name='PUBG Players'
    ))
    
    fig.update_layout(
        title={
            'text': f'PUBG Player Performance Analysis<br><sub>Bubble size = Kills | Color = {color_col.title()}</sub>',
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 16, 'family': 'Arial Black'}
        },
        xaxis_title=x_col.replace('Distance', ' Distance').replace('Dealt', ' Dealt').title(),
        yaxis_title=y_col.replace('Distance', ' Distance').replace('Dealt', ' Dealt').title(),
        width=1000,
        height=700,
        template='plotly_white',
        showlegend=False
    )
    
    fig.show()
    print(f"✅ Interactive PUBG scatter plot displayed ({len(pubg_data):,} players)")
    
else:
    print("⚠️ Required columns not found - creating simple interactive plot")
    
    # Simple scatter with available data
    if len(numerical_cols) >= 2:
        fig = px.scatter(pubg_data, 
                        x=numerical_cols[0], 
                        y=numerical_cols[1],
                        title=f'Interactive Scatter: {numerical_cols[0]} vs {numerical_cols[1]}',
                        template='plotly_white')
        fig.show()

# Create advanced PUBG visualizations using plotly subplots
if 'kills' in pubg_data.columns:
    print("🎮 Creating advanced PUBG dashboard...")
    
    # Create subplot figure
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('Kills Distribution', 'Damage vs Walk Distance', 
                       'Win Placement Distribution', 'Performance Correlation'),
        specs=[[{"type": "histogram"}, {"type": "scatter"}],
               [{"type": "histogram"}, {"type": "scatter"}]]
    )
    
    # 1. Kills histogram
    fig.add_trace(
        go.Histogram(x=pubg_data['kills'], name='Kills', nbinsx=20),
        row=1, col=1
    )
    
    # 2. Damage vs Walk Distance scatter
    if 'damageDealt' in pubg_data.columns and 'walkDistance' in pubg_data.columns:
        fig.add_trace(
            go.Scatter(
                x=pubg_data['walkDistance'], 
                y=pubg_data['damageDealt'],
                mode='markers',
                marker=dict(size=4, opacity=0.6, color=pubg_data['kills'], colorscale='Viridis'),
                name='Players'
            ),
            row=1, col=2
        )
    
    # 3. Win placement histogram
    if 'winPlacePerc' in pubg_data.columns:
        fig.add_trace(
            go.Histogram(x=pubg_data['winPlacePerc'], name='Win Placement', nbinsx=30),
            row=2, col=1
        )
    
    # 4. Performance correlation (kills vs damage)
    if 'damageDealt' in pubg_data.columns:
        fig.add_trace(
            go.Scatter(
                x=pubg_data['kills'], 
                y=pubg_data['damageDealt'],
                mode='markers',
                marker=dict(size=6, opacity=0.6),
                name='Performance'
            ),
            row=2, col=2
        )
    
    # Update layout
    fig.update_layout(
        title_text="PUBG Player Performance Dashboard",
        title_x=0.5,
        height=800,
        showlegend=False
    )
    
    fig.show()
    print("✅ Advanced PUBG dashboard created!")
else:
    print("⚠️ Advanced visualizations require 'kills' column")

# Final summary and diagnostics
print("🎮 PUBG VISUALIZATION SUMMARY")
print("=" * 50)

if 'pubg_data' in locals() and pubg_data is not None:
    print(f"✅ Dataset loaded: {pubg_data.shape}")
    print(f"💾 Memory usage: {pubg_data.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
    print(f"📊 Columns available: {len(pubg_data.columns)}")
    
    # Check key PUBG columns
    key_cols = ['kills', 'damageDealt', 'walkDistance', 'winPlacePerc', 'assists', 'heals']
    found_cols = [col for col in key_cols if col in pubg_data.columns]
    print(f"🎯 Key PUBG columns found: {found_cols}")
    
    print(f"\n✅ VISUALIZATIONS STATUS:")
    print(f"  📈 Matplotlib: Configured and tested")
    print(f"  🎨 Plotly: Interactive plots created")
    print(f"  📊 Dashboard: Advanced multi-plot layout")
    
else:
    print(f"❌ No data loaded")

print(f"\n🔧 CONFIGURATION:")
print(f"✅ Matplotlib backend: {plt.get_backend()}")
print(f"✅ Plotly renderer: {pio.renderers.default}")

print(f"\n💡 TROUBLESHOOTING:")
print(f"If plots are not showing:")
print(f"1. Restart Jupyter kernel: Kernel → Restart & Clear Output")
print(f"2. Try different Plotly renderer: pio.renderers.default = 'iframe'")
print(f"3. Check browser console for JavaScript errors")
print(f"4. Ensure %matplotlib inline is executed")

print(f"\n🚀 SUCCESS INDICATORS:")
print(f"✅ You should see:")
print(f"  - Sine wave plots (matplotlib and plotly tests)")
print(f"  - PUBG data histograms and scatter plots")
print(f"  - Interactive hover information on plotly charts")
print(f"  - Multi-panel dashboard with PUBG statistics")

print(f"\n🎯 NEXT STEPS:")
print(f"1. Copy working configuration to your main analysis notebook")
print(f"2. Use the data loading patterns for large CSV files")
print(f"3. Adapt visualization code for your specific analysis")
print(f"4. Consider saving processed data as Parquet for faster loading")

# Test alternative plotly renderers if needed
print("🔧 ALTERNATIVE PLOTLY RENDERERS:")
print("If plots are not showing, try these commands:")
print("\n# For most Jupyter environments:")
print("pio.renderers.default = 'iframe'")
print("\n# For JupyterLab:")
print("pio.renderers.default = 'jupyterlab'")
print("\n# For Google Colab:")
print("pio.renderers.default = 'colab'")
print("\n# For VS Code:")
print("pio.renderers.default = 'vscode'")

print(f"\nCurrent renderer: {pio.renderers.default}")
print(f"Available renderers: {list(pio.renderers)}")

# Uncomment the line below to try iframe renderer
# pio.renderers.default = 'iframe'
# print("Switched to iframe renderer - re-run visualization cells to test")