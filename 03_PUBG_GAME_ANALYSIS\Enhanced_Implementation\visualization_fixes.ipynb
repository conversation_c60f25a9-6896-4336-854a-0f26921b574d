# Core Data Science Libraries
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Statistical Analysis
from scipy import stats
from scipy.stats import normaltest, skew, kurtosis

# Static Visualization
import matplotlib.pyplot as plt
import seaborn as sns

# Interactive Visualization
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.io as pio

# System libraries
import os
import time
import datetime

# Configure matplotlib for Jupyter
%matplotlib inline
print("✓ Matplotlib inline mode activated")

# Set matplotlib style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
print("✓ Matplotlib style configured")

# Configure Plotly for Jupyter (try multiple renderers)
renderers_to_try = [
    'notebook_connected',  # Modern Jupyter notebooks
    'iframe',              # Fallback for most environments
    'jupyterlab',          # JupyterLab specific
    'colab',               # Google Colab
    'browser'              # Opens in browser
]

for renderer in renderers_to_try:
    try:
        pio.renderers.default = renderer
        print(f"✓ Plotly renderer set to: {renderer}")
        break
    except:
        continue
else:
    print("⚠ Could not set Plotly renderer")

# Set pandas display options
pd.set_option('display.max_columns', None)
pd.set_option('display.precision', 3)
print("✓ Pandas display options configured")

def diagnose_visualization_issues():
    """Diagnose common visualization issues in Jupyter notebooks."""
    print("VISUALIZATION DIAGNOSTICS")
    print("=" * 50)
    
    # Check if we're in Jupyter
    try:
        from IPython import get_ipython
        ipython = get_ipython()
        if ipython is not None:
            print("✓ Running in Jupyter environment")
        else:
            print("⚠ Not running in Jupyter environment")
    except ImportError:
        print("⚠ IPython not available")
    
    return True

# Run diagnostics
diagnose_visualization_issues()

# Check backends and versions
print(f"Matplotlib backend: {plt.get_backend()}")
print(f"Plotly default renderer: {pio.renderers.default}")
print(f"Matplotlib version: {plt.__version__}")
print(f"Plotly version: {pio.__version__}")

# Create sample data for matplotlib test
x = np.linspace(0, 10, 100)
y = np.sin(x)
print("Sample data created for matplotlib test")

# Create matplotlib test plot
fig, ax = plt.subplots(figsize=(8, 5))
ax.plot(x, y, label='sin(x)', linewidth=2)
ax.set_title('Matplotlib Test Plot', fontsize=14, fontweight='bold')
ax.set_xlabel('X values')
ax.set_ylabel('Y values')
ax.legend()
ax.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print("✓ Matplotlib test completed")

# Create Plotly test plot
fig = go.Figure()
fig.add_trace(go.Scatter(
    x=x, 
    y=y, 
    mode='lines',
    name='sin(x)',
    line=dict(color='blue', width=2)
))

print("Plotly figure created")

# Update Plotly layout
fig.update_layout(
    title='Plotly Test Plot',
    xaxis_title='X values',
    yaxis_title='Y values',
    template='plotly_white',
    width=800,
    height=500
)

print("Layout updated")

# Display Plotly plot
fig.show()
print("✓ Plotly test completed")

# Generate sample PUBG data
np.random.seed(42)
n_samples = 1000

sample_data = pd.DataFrame({
    'kills': np.random.poisson(2, n_samples),
    'damageDealt': np.random.normal(500, 200, n_samples),
    'walkDistance': np.random.normal(1000, 300, n_samples),
    'winPlacePerc': np.random.beta(2, 5, n_samples)
})

print(f"Sample data created: {sample_data.shape}")

# Clean sample data (ensure non-negative values)
sample_data['damageDealt'] = np.maximum(sample_data['damageDealt'], 0)
sample_data['walkDistance'] = np.maximum(sample_data['walkDistance'], 0)

print("Data cleaned:")
print(sample_data.describe())

# Create interactive scatter plot
pubg_fig = go.Figure()

pubg_fig.add_trace(go.Scatter(
    x=sample_data['walkDistance'],
    y=sample_data['damageDealt'],
    mode='markers',
    marker=dict(
        size=(sample_data['kills'] + 1) * 8,
        color=sample_data['winPlacePerc'],
        colorscale='Viridis',
        opacity=0.7,
        line=dict(width=1, color='black')
    ),
    name='Players'
))

print("PUBG scatter plot created")

# Add hover information
pubg_fig.update_traces(
    hovertemplate='<b>Walk Distance</b>: %{x:.0f}m<br>' +
                 '<b>Damage Dealt</b>: %{y:.0f}<br>' +
                 '<b>Kills</b>: %{customdata[0]}<br>' +
                 '<b>Win Placement</b>: %{customdata[1]:.3f}<extra></extra>',
    customdata=np.column_stack((sample_data['kills'], sample_data['winPlacePerc']))
)

print("Hover information added")

# Update layout
pubg_fig.update_layout(
    title={
        'text': 'Sample PUBG Player Performance Analysis<br><sub>Bubble size = Kills | Color = Win Placement</sub>',
        'x': 0.5,
        'xanchor': 'center',
        'font': {'size': 16, 'family': 'Arial Black'}
    },
    xaxis_title='Walk Distance (meters)',
    yaxis_title='Damage Dealt',
    width=900,
    height=600,
    template='plotly_white'
)

print("Layout configured")

# Add colorbar
pubg_fig.update_traces(
    marker_colorbar=dict(
        title="Win Placement Percentile",
        titleside="right"
    )
)

print("Colorbar added")

# Display the PUBG visualization
pubg_fig.show()
print("✓ Sample PUBG visualization displayed")

# Test different renderers if plots don't show
print("Available Plotly renderers:")
for renderer in pio.renderers:
    print(f"  - {renderer}")

print("VISUALIZATION SETUP COMPLETE!")
print("=" * 40)
print("✓ Matplotlib configured and tested")
print("✓ Plotly configured and tested")
print("✓ Sample PUBG visualization created")
print("\nIf plots are not showing:")
print("1. Try: pio.renderers.default = 'iframe'")
print("2. Try: pio.renderers.default = 'jupyterlab'")
print("3. Try: pio.renderers.default = 'colab'")
print("4. Restart kernel and re-run cells")