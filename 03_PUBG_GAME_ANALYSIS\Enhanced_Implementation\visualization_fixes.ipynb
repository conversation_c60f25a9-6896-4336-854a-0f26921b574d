# Core Data Science Libraries
import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')
import gc  # For memory management

# Statistical Analysis
from scipy import stats
from scipy.stats import normaltest, skew, kurtosis

# Static Visualization
import matplotlib.pyplot as plt
import seaborn as sns

# Interactive Visualization
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.io as pio

# System libraries
import os
import time
import datetime



# Configure matplotlib for Jupyter
%matplotlib inline
print("✓ Matplotlib inline mode activated")

# Set matplotlib style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
print("✓ Matplotlib style configured")

# Configure Plotly for Jupyter (try multiple renderers)
renderers_to_try = [
    'notebook_connected',  # Modern Jupyter notebooks
    'iframe',              # Fallback for most environments
    'jupyterlab',          # JupyterLab specific
    'colab',               # Google Colab
    'browser'              # Opens in browser
]

for renderer in renderers_to_try:
    try:
        pio.renderers.default = renderer
        print(f"✓ Plotly renderer set to: {renderer}")
        break
    except:
        continue
else:
    print("⚠ Could not set Plotly renderer")

# Set pandas display options
pd.set_option('display.max_columns', None)
pd.set_option('display.precision', 3)
print("✓ Pandas display options configured")

def diagnose_visualization_issues():
    """Diagnose common visualization issues in Jupyter notebooks."""
    print("VISUALIZATION DIAGNOSTICS")
    print("=" * 50)
    
    # Check if we're in Jupyter
    try:
        from IPython import get_ipython
        ipython = get_ipython()
        if ipython is not None:
            print("✓ Running in Jupyter environment")
        else:
            print("⚠ Not running in Jupyter environment")
    except ImportError:
        print("⚠ IPython not available")
    
    return True

# Run diagnostics
diagnose_visualization_issues()

# Check backends and versions
print(f"Matplotlib backend: {plt.get_backend()}")
print(f"Plotly default renderer: {pio.renderers.default}")
print(f"Matplotlib version: {plt.__version__}")
print(f"Plotly version: {pio.__version__}")

# Create sample data for matplotlib test
x = np.linspace(0, 10, 100)
y = np.sin(x)
print("Sample data created for matplotlib test")

# Create matplotlib test plot
fig, ax = plt.subplots(figsize=(8, 5))
ax.plot(x, y, label='sin(x)', linewidth=2)
ax.set_title('Matplotlib Test Plot', fontsize=14, fontweight='bold')
ax.set_xlabel('X values')
ax.set_ylabel('Y values')
ax.legend()
ax.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print("✓ Matplotlib test completed")

# Create Plotly test plot
fig = go.Figure()
fig.add_trace(go.Scatter(
    x=x, 
    y=y, 
    mode='lines',
    name='sin(x)',
    line=dict(color='blue', width=2)
))

print("Plotly figure created")

# Update Plotly layout
fig.update_layout(
    title='Plotly Test Plot',
    xaxis_title='X values',
    yaxis_title='Y values',
    template='plotly_white',
    width=800,
    height=500
)

print("Layout updated")

# Display Plotly plot
fig.show()
print("✓ Plotly test completed")

# Define data path
DATA_PATH = '../data/pubg.csv'

# Check file existence and size
if os.path.exists(DATA_PATH):
    file_size = os.path.getsize(DATA_PATH)
    print(f"📁 File found: {DATA_PATH}")
    print(f"📊 Size: {file_size:,} bytes ({file_size/1024**2:.2f} MB)")
    
    if file_size > 100 * 1024 * 1024:  # > 100 MB
        print("⚠️  Large file detected - using optimized loading")
        USE_OPTIMIZED_LOADING = True
    else:
        USE_OPTIMIZED_LOADING = False
else:
    print(f"❌ File not found: {DATA_PATH}")
    USE_OPTIMIZED_LOADING = False

# Peek at file structure first
print("🔍 Examining PUBG dataset structure...")

try:
    df_peek = pd.read_csv(DATA_PATH, nrows=3, low_memory=False)
    
    print(f"✅ Successfully read {len(df_peek)} sample rows")
    print(f"📋 Columns ({len(df_peek.columns)}):")
    
    for i, col in enumerate(df_peek.columns, 1):
        dtype = str(df_peek[col].dtype)
        print(f"  {i:2d}. {col:<20} ({dtype})")
        
    print(f"\n📊 Sample data:")
    print(df_peek.head())
        
except Exception as e:
    print(f"❌ Error reading file: {e}")
    df_peek = None

# Load optimized sample of PUBG data
def load_pubg_sample(file_path, sample_size=10000):
    """
    Load a sample of PUBG data for visualization.
    """
    print(f"📊 Loading PUBG sample of {sample_size:,} rows...")
    
    try:
        # Load sample with optimized data types
        dtype_dict = {
            'Id': 'string',
            'groupId': 'string', 
            'matchId': 'string',
            'matchType': 'category'
        }
        
        df_sample = pd.read_csv(file_path, 
                               nrows=sample_size, 
                               low_memory=False,
                               dtype=dtype_dict)
        
        print(f"✅ Sample loaded: {df_sample.shape}")
        print(f"💾 Memory usage: {df_sample.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
        
        return df_sample
        
    except Exception as e:
        print(f"❌ Error loading PUBG data: {e}")
        return None

# Load the PUBG data
if USE_OPTIMIZED_LOADING:
    pubg_data = load_pubg_sample(DATA_PATH, sample_size=15000)
else:
    try:
        pubg_data = pd.read_csv(DATA_PATH, low_memory=False)
        print(f"✅ Full dataset loaded: {pubg_data.shape}")
    except:
        pubg_data = load_pubg_sample(DATA_PATH, sample_size=15000)

# Analyze the loaded PUBG data
if pubg_data is not None:
    print("📊 PUBG DATASET ANALYSIS")
    print("=" * 40)
    print(f"Shape: {pubg_data.shape}")
    print(f"Memory usage: {pubg_data.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
    print(f"Missing values: {pubg_data.isnull().sum().sum()}")
    
    # Check for key PUBG columns
    key_columns = ['kills', 'damageDealt', 'walkDistance', 'winPlacePerc']
    available_columns = [col for col in key_columns if col in pubg_data.columns]
    
    print(f"\n🎮 Key PUBG columns available: {available_columns}")
    
    if available_columns:
        print(f"\n📈 Basic statistics:")
        print(pubg_data[available_columns].describe())
    
    # Clean memory
    gc.collect()
    
else:
    print("❌ No PUBG data available - creating synthetic data for testing")
    # Fallback to synthetic data
    np.random.seed(42)
    n_samples = 5000
    
    pubg_data = pd.DataFrame({
        'kills': np.random.poisson(2, n_samples),
        'damageDealt': np.random.normal(500, 200, n_samples),
        'walkDistance': np.random.normal(1000, 300, n_samples),
        'winPlacePerc': np.random.beta(2, 5, n_samples)
    })
    
    # Clean synthetic data
    pubg_data['damageDealt'] = np.maximum(pubg_data['damageDealt'], 0)
    pubg_data['walkDistance'] = np.maximum(pubg_data['walkDistance'], 0)
    
    print(f"✅ Synthetic PUBG data created: {pubg_data.shape}")

# Create PUBG-specific visualizations
if pubg_data is not None and 'kills' in pubg_data.columns:
    print("🎨 Creating PUBG visualizations...")
    
    # 1. Kills Distribution (Matplotlib)
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Histogram of kills
    ax1.hist(pubg_data['kills'], bins=range(0, min(pubg_data['kills'].max() + 2, 21)), 
             alpha=0.7, edgecolor='black', color='skyblue')
    ax1.set_title('Distribution of Kills per Match', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Number of Kills')
    ax1.set_ylabel('Frequency')
    ax1.grid(True, alpha=0.3)
    
    # Box plot of damage dealt
    if 'damageDealt' in pubg_data.columns:
        ax2.boxplot(pubg_data['damageDealt'], patch_artist=True, 
                   boxprops=dict(facecolor='lightcoral', alpha=0.7))
        ax2.set_title('Distribution of Damage Dealt', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Damage Dealt')
        ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    print("✅ Matplotlib PUBG visualizations created")
else:
    print("❌ Cannot create PUBG visualizations - data not available")

# Create interactive PUBG scatter plot
if pubg_data is not None:
    # Check which columns are available
    x_col = 'walkDistance' if 'walkDistance' in pubg_data.columns else pubg_data.select_dtypes(include=[np.number]).columns[0]
    y_col = 'damageDealt' if 'damageDealt' in pubg_data.columns else pubg_data.select_dtypes(include=[np.number]).columns[1]
    size_col = 'kills' if 'kills' in pubg_data.columns else None
    color_col = 'winPlacePerc' if 'winPlacePerc' in pubg_data.columns else pubg_data.select_dtypes(include=[np.number]).columns[-1]
    
    print(f"📊 Creating scatter plot with:")
    print(f"   X-axis: {x_col}")
    print(f"   Y-axis: {y_col}")
    print(f"   Size: {size_col}")
    print(f"   Color: {color_col}")
    
    pubg_fig = go.Figure()
    
    # Prepare marker size
    if size_col:
        marker_size = (pubg_data[size_col] + 1) * 6
    else:
        marker_size = 8
    
    pubg_fig.add_trace(go.Scatter(
        x=pubg_data[x_col],
        y=pubg_data[y_col],
        mode='markers',
        marker=dict(
            size=marker_size,
            color=pubg_data[color_col],
            colorscale='Viridis',
            opacity=0.6,
            line=dict(width=0.5, color='black'),
            colorbar=dict(
                title=color_col.replace('Perc', ' Percentile').title(),
                titleside="right"
            )
        ),
        name='PUBG Players'
    ))
    
    print("✅ PUBG scatter plot created")
else:
    print("❌ Cannot create scatter plot - no data available")

# Add hover information and layout
if pubg_data is not None and 'pubg_fig' in locals():
    # Create custom hover template
    hover_template = f'<b>{x_col.title()}</b>: %{{x:.0f}}<br>'
    hover_template += f'<b>{y_col.title()}</b>: %{{y:.0f}}<br>'
    
    if size_col:
        hover_template += f'<b>{size_col.title()}</b>: %{{customdata[0]}}<br>'
    hover_template += f'<b>{color_col.title()}</b>: %{{customdata[1]:.3f}}<extra></extra>'
    
    # Prepare custom data
    if size_col:
        custom_data = np.column_stack((pubg_data[size_col], pubg_data[color_col]))
    else:
        custom_data = pubg_data[color_col].values.reshape(-1, 1)
        hover_template = f'<b>{x_col.title()}</b>: %{{x:.0f}}<br>'
        hover_template += f'<b>{y_col.title()}</b>: %{{y:.0f}}<br>'
        hover_template += f'<b>{color_col.title()}</b>: %{{customdata[0]:.3f}}<extra></extra>'
    
    pubg_fig.update_traces(
        hovertemplate=hover_template,
        customdata=custom_data
    )
    
    print("✅ Hover information added")
else:
    print("⚠️ Skipping hover update - no figure available")

# Update layout for PUBG visualization
if pubg_data is not None and 'pubg_fig' in locals():
    # Create dynamic title
    title_text = 'Real PUBG Player Performance Analysis'
    if size_col and color_col:
        subtitle = f'<sub>Bubble size = {size_col.title()} | Color = {color_col.title()}</sub>'
    else:
        subtitle = f'<sub>Sample size: {len(pubg_data):,} players</sub>'
    
    pubg_fig.update_layout(
        title={
            'text': f'{title_text}<br>{subtitle}',
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 16, 'family': 'Arial Black'}
        },
        xaxis_title=x_col.replace('Distance', ' Distance').replace('Dealt', ' Dealt').title(),
        yaxis_title=y_col.replace('Distance', ' Distance').replace('Dealt', ' Dealt').title(),
        width=1000,
        height=700,
        template='plotly_white',
        showlegend=False
    )
    
    print("✅ Layout configured for PUBG data")
else:
    print("⚠️ Skipping layout update - no figure available")

# Display the PUBG visualization
if pubg_data is not None and 'pubg_fig' in locals():
    pubg_fig.show()
    print("✅ Real PUBG visualization displayed")
    print(f"📊 Showing {len(pubg_data):,} PUBG player records")
else:
    print("❌ No PUBG visualization to display")

# Create PUBG-specific interactive plots
if pubg_data is not None:
    print("🎮 Creating additional PUBG visualizations...")
    
    # 1. Interactive histogram of kills
    if 'kills' in pubg_data.columns:
        kills_fig = go.Figure()
        kills_fig.add_trace(go.Histogram(
            x=pubg_data['kills'],
            nbinsx=min(30, pubg_data['kills'].max() + 1),
            name='Kills Distribution',
            opacity=0.7,
            marker_color='lightblue'
        ))
        
        kills_fig.update_layout(
            title='PUBG: Distribution of Kills per Match',
            xaxis_title='Number of Kills',
            yaxis_title='Number of Players',
            template='plotly_white',
            width=800,
            height=500
        )
        
        kills_fig.show()
        print("✅ Kills distribution plot created")
    
    # 2. Win placement analysis
    if 'winPlacePerc' in pubg_data.columns:
        win_fig = go.Figure()
        win_fig.add_trace(go.Histogram(
            x=pubg_data['winPlacePerc'],
            nbinsx=50,
            name='Win Placement Distribution',
            opacity=0.7,
            marker_color='lightgreen'
        ))
        
        win_fig.update_layout(
            title='PUBG: Win Placement Percentile Distribution',
            xaxis_title='Win Placement Percentile',
            yaxis_title='Number of Players',
            template='plotly_white',
            width=800,
            height=500
        )
        
        win_fig.show()
        print("✅ Win placement distribution plot created")
    
    print("🎯 All PUBG visualizations completed!")
else:
    print("❌ Cannot create additional visualizations - no PUBG data available")

# Test different renderers if plots don't show
print("Available Plotly renderers:")
for renderer in pio.renderers:
    print(f"  - {renderer}")
    
print(f"\nCurrent renderer: {pio.renderers.default}")
print("\nIf plots are not showing, try these commands:")
print("  pio.renderers.default = 'iframe'")
print("  pio.renderers.default = 'jupyterlab'")
print("  pio.renderers.default = 'colab'")

print("🎮 PUBG VISUALIZATION SETUP COMPLETE!")
print("=" * 50)

if pubg_data is not None:
    print(f"✅ Successfully loaded PUBG dataset: {pubg_data.shape}")
    print(f"💾 Memory usage: {pubg_data.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
    print(f"📊 Available columns: {len(pubg_data.columns)}")
    
    # List key PUBG columns found
    key_cols = ['kills', 'damageDealt', 'walkDistance', 'winPlacePerc', 'assists', 'heals']
    found_cols = [col for col in key_cols if col in pubg_data.columns]
    print(f"🎯 Key PUBG columns found: {found_cols}")
    
    print(f"\n✅ VISUALIZATIONS CREATED:")
    print(f"  📈 Matplotlib plots (kills distribution, damage box plot)")
    print(f"  🎨 Interactive scatter plot (performance analysis)")
    print(f"  📊 Interactive histograms (kills, win placement)")
    
else:
    print(f"⚠️ Using synthetic data for testing")
    print(f"📁 Original file: ../data/pubg.csv (629 MB)")

print(f"\n🔧 CONFIGURATION STATUS:")
print(f"✅ Matplotlib backend: {plt.get_backend()}")
print(f"✅ Plotly renderer: {pio.renderers.default}")
print(f"✅ Memory management: Enabled")

print(f"\n💡 NEXT STEPS:")
print(f"1. ✅ Copy this configuration to your main notebook")
print(f"2. ✅ Use the optimized data loading approach")
print(f"3. ✅ Apply the same visualization patterns")
print(f"4. 🔄 For full dataset analysis, use chunked processing")
print(f"5. 💾 Consider saving processed data as Parquet format")

print(f"\n🚨 TROUBLESHOOTING:")
print(f"If visualizations don't show:")
print(f"  1. Restart Jupyter kernel")
print(f"  2. Try different Plotly renderers (see section 9)")
print(f"  3. Check browser console for errors")
print(f"  4. Ensure sufficient system memory")