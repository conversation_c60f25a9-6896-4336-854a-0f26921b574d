# ===== COMPLETE FIXED MODEL SAVING - INCLUDING XGBOOST, LIGHTGBM, CATBOOST =====
# Fixes all model saving errors and includes all advanced gradient boosting models

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge, ElasticNet
from sklearn.neural_network import MLPRegressor
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
import joblib
import pickle
import json
import os
import subprocess
import sys
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

print("🔧 COMPLETE FIXED MODEL SAVING - ALL GRADIENT BOOSTING MODELS")
print("=" * 70)

# ===== INSTALL AND IMPORT ADVANCED GRADIENT BOOSTING LIBRARIES =====

def install_and_import_package(package_name, import_name=None):
    """Install and import package with error handling"""
    if import_name is None:
        import_name = package_name
    
    try:
        # Try to import first
        if package_name == 'xgboost':
            import xgboost as xgb
            print(f"✅ {package_name} already available")
            return True, xgb
        elif package_name == 'lightgbm':
            import lightgbm as lgb
            print(f"✅ {package_name} already available")
            return True, lgb
        elif package_name == 'catboost':
            import catboost as cb
            print(f"✅ {package_name} already available")
            return True, cb
    except ImportError:
        print(f"⚠️ {package_name} not found - installing...")
        try:
            # Install the package
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package_name, "--quiet"
            ])
            
            # Import after installation
            if package_name == 'xgboost':
                import xgboost as xgb
                print(f"✅ {package_name} installed and imported successfully")
                return True, xgb
            elif package_name == 'lightgbm':
                import lightgbm as lgb
                print(f"✅ {package_name} installed and imported successfully")
                return True, lgb
            elif package_name == 'catboost':
                import catboost as cb
                print(f"✅ {package_name} installed and imported successfully")
                return True, cb
                
        except Exception as e:
            print(f"❌ Failed to install {package_name}: {e}")
            return False, None
    
    return False, None

# Install and import advanced libraries
print(f"\n📦 INSTALLING ADVANCED GRADIENT BOOSTING LIBRARIES:")
XGBOOST_AVAILABLE, xgb = install_and_import_package('xgboost')
LIGHTGBM_AVAILABLE, lgb = install_and_import_package('lightgbm')
CATBOOST_AVAILABLE, cb = install_and_import_package('catboost')

print(f"\n📊 LIBRARY STATUS:")
print(f"  • XGBoost: {'✅ Available' if XGBOOST_AVAILABLE else '❌ Not Available'}")
print(f"  • LightGBM: {'✅ Available' if LIGHTGBM_AVAILABLE else '❌ Not Available'}")
print(f"  • CatBoost: {'✅ Available' if CATBOOST_AVAILABLE else '❌ Not Available'}")

# ===== COMPREHENSIVE MODEL SAVER WITH ALL MODELS =====

class CompleteModelSaver:
    """Complete model saver including XGBoost, LightGBM, and CatBoost"""
    
    def __init__(self):
        self.models = {}
        self.model_performance = {}
        self.saved_files = []
        self.errors = []
        
    def create_all_models(self, X, y):
        """Create and train ALL models including advanced gradient boosting"""
        
        print(f"\n🤖 CREATING AND TRAINING ALL MODELS")
        print("-" * 45)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # ===== BASIC ML MODELS =====
        basic_models = {
            'LinearRegression': LinearRegression(),
            'RandomForest': RandomForestRegressor(
                n_estimators=100, 
                max_depth=10,
                random_state=42, 
                n_jobs=-1
            ),
            'Ridge': Ridge(alpha=1.0, random_state=42),
            'ElasticNet': ElasticNet(alpha=0.1, random_state=42, max_iter=1000),
            'GradientBoosting': GradientBoostingRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42
            )
        }
        
        # ===== ADVANCED GRADIENT BOOSTING MODELS =====
        if XGBOOST_AVAILABLE:
            basic_models['XGBoost'] = xgb.XGBRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42,
                n_jobs=-1,
                verbosity=0,
                eval_metric='rmse'
            )
            print(f"  ✅ XGBoost model added")
        
        if LIGHTGBM_AVAILABLE:
            basic_models['LightGBM'] = lgb.LGBMRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42,
                n_jobs=-1,
                verbosity=-1,
                force_col_wise=True
            )
            print(f"  ✅ LightGBM model added")
        
        if CATBOOST_AVAILABLE:
            basic_models['CatBoost'] = cb.CatBoostRegressor(
                iterations=100,
                depth=6,
                learning_rate=0.1,
                random_seed=42,
                verbose=False,
                allow_writing_files=False
            )
            print(f"  ✅ CatBoost model added")
        
        print(f"\n📊 Training {len(basic_models)} models...")
        
        # Train and evaluate each model
        for model_name, model in basic_models.items():
            try:
                print(f"\n  🔧 Training {model_name}...")
                
                # Train model
                model.fit(X_train, y_train)
                
                # Make predictions
                y_pred = model.predict(X_test)
                
                # Calculate metrics
                r2 = r2_score(y_test, y_pred)
                rmse = np.sqrt(mean_squared_error(y_test, y_pred))
                mae = mean_absolute_error(y_test, y_pred)
                
                # Cross-validation
                cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='r2')
                
                # Store model and performance
                self.models[model_name] = model
                self.model_performance[model_name] = {
                    'r2_score': float(r2),
                    'rmse': float(rmse),
                    'mae': float(mae),
                    'cv_mean': float(cv_scores.mean()),
                    'cv_std': float(cv_scores.std())
                }
                
                print(f"    ✅ R² Score: {r2:.4f}")
                print(f"    📊 RMSE: ${rmse:,.0f}")
                print(f"    📈 MAE: ${mae:,.0f}")
                print(f"    🔄 CV Score: {cv_scores.mean():.4f} (±{cv_scores.std():.4f})")
                
            except Exception as e:
                print(f"    ❌ Error training {model_name}: {e}")
                self.errors.append(f"{model_name}: {e}")
        
        # ===== NEURAL NETWORK MODELS =====
        print(f"\n🧠 TRAINING NEURAL NETWORK MODELS:")
        
        # Scale data for neural networks
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        neural_models = {
            'SimpleNeuralNetwork': MLPRegressor(
                hidden_layer_sizes=(50,),
                activation='relu',
                solver='adam',
                alpha=0.001,
                max_iter=500,
                random_state=42
            ),
            'DeepNeuralNetwork': MLPRegressor(
                hidden_layer_sizes=(100, 50, 25),
                activation='relu',
                solver='adam',
                alpha=0.001,
                max_iter=500,
                random_state=42
            ),
            'WideNeuralNetwork': MLPRegressor(
                hidden_layer_sizes=(200, 100),
                activation='relu',
                solver='adam',
                alpha=0.001,
                max_iter=500,
                random_state=42
            )
        }
        
        for model_name, model in neural_models.items():
            try:
                print(f"\n  🧠 Training {model_name}...")
                
                # Train model
                model.fit(X_train_scaled, y_train)
                
                # Make predictions
                y_pred = model.predict(X_test_scaled)
                
                # Calculate metrics
                r2 = r2_score(y_test, y_pred)
                rmse = np.sqrt(mean_squared_error(y_test, y_pred))
                mae = mean_absolute_error(y_test, y_pred)
                
                # Cross-validation
                cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=5, scoring='r2')
                
                # Store model and performance (with scaler)
                self.models[model_name] = {
                    'model': model,
                    'scaler': scaler
                }
                self.model_performance[model_name] = {
                    'r2_score': float(r2),
                    'rmse': float(rmse),
                    'mae': float(mae),
                    'cv_mean': float(cv_scores.mean()),
                    'cv_std': float(cv_scores.std())
                }
                
                print(f"    ✅ R² Score: {r2:.4f}")
                print(f"    📊 RMSE: ${rmse:,.0f}")
                print(f"    📈 MAE: ${mae:,.0f}")
                print(f"    🔄 CV Score: {cv_scores.mean():.4f} (±{cv_scores.std():.4f})")
                
            except Exception as e:
                print(f"    ❌ Error training {model_name}: {e}")
                self.errors.append(f"{model_name}: {e}")
        
        return X_train, X_test, y_train, y_test
    
    def save_all_models(self):
        """Save all models with proper error handling"""
        
        print(f"\n💾 SAVING ALL MODELS")
        print("-" * 25)
        
        # Create models directory
        models_dir = "saved_models"
        if not os.path.exists(models_dir):
            os.makedirs(models_dir)
        
        # Save basic ML models (including XGBoost, LightGBM, CatBoost)
        print(f"\n🤖 SAVING BASIC MACHINE LEARNING MODELS:")
        for model_name, model in self.models.items():
            if not isinstance(model, dict):  # Basic models (not neural networks)
                try:
                    filename = f"{models_dir}/{model_name}_model.joblib"
                    joblib.dump(model, filename)
                    self.saved_files.append(filename)
                    print(f"  ✅ {model_name}: Saved successfully")
                except Exception as e:
                    print(f"  ❌ {model_name}: Error saving - {e}")
                    self.errors.append(f"{model_name}: {e}")
        
        # Save neural network models
        print(f"\n🧠 SAVING NEURAL NETWORK MODELS:")
        for model_name, model_data in self.models.items():
            if isinstance(model_data, dict) and 'model' in model_data:  # Neural networks
                try:
                    # Save model
                    model_filename = f"{models_dir}/{model_name}_model.joblib"
                    joblib.dump(model_data['model'], model_filename)
                    
                    # Save scaler
                    scaler_filename = f"{models_dir}/{model_name}_scaler.joblib"
                    joblib.dump(model_data['scaler'], scaler_filename)
                    
                    self.saved_files.extend([model_filename, scaler_filename])
                    print(f"  ✅ {model_name}: 2 files saved")
                except Exception as e:
                    print(f"  ❌ {model_name}: Error saving - {e}")
                    self.errors.append(f"{model_name}: {e}")
        
        # Save model performance summary
        try:
            performance_filename = f"{models_dir}/model_performance_summary.json"
            with open(performance_filename, 'w') as f:
                json.dump(self.model_performance, f, indent=2)
            self.saved_files.append(performance_filename)
            print(f"\n📊 Model performance summary saved")
        except Exception as e:
            print(f"\n❌ Error saving performance summary: {e}")
        
        return len(self.saved_files)
    
    def display_model_comparison(self):
        """Display comparison of all models"""
        
        print(f"\n📊 MODEL PERFORMANCE COMPARISON")
        print("-" * 50)
        
        # Create comparison DataFrame
        comparison_data = []
        for model_name, perf in self.model_performance.items():
            comparison_data.append({
                'Model': model_name,
                'R² Score': f"{perf['r2_score']:.4f}",
                'RMSE': f"${perf['rmse']:,.0f}",
                'MAE': f"${perf['mae']:,.0f}",
                'CV Score': f"{perf['cv_mean']:.4f} (±{perf['cv_std']:.4f})"
            })
        
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df = comparison_df.sort_values('R² Score', ascending=False)
        
        print(comparison_df.to_string(index=False))
        
        # Find best model
        best_model = max(self.model_performance.items(), key=lambda x: x[1]['r2_score'])
        print(f"\n🏆 BEST MODEL: {best_model[0]}")
        print(f"🎯 Best R² Score: {best_model[1]['r2_score']:.4f}")
        
        return comparison_df

# ===== MAIN EXECUTION =====

# Create sample data if df_processed is not available
if 'df_processed' not in globals() or df_processed is None:
    print(f"\n⚠️ df_processed not found - creating sample data for demonstration")
    
    # Create realistic sample house price data
    np.random.seed(42)
    n_samples = 1000
    
    sample_data = pd.DataFrame({
        'GrLivArea': np.random.normal(1500, 400, n_samples),
        'OverallQual': np.random.randint(1, 11, n_samples),
        'YearBuilt': np.random.randint(1950, 2024, n_samples),
        'TotalBsmtSF': np.random.normal(1000, 300, n_samples),
        'GarageCars': np.random.randint(0, 4, n_samples),
        'FullBath': np.random.randint(1, 4, n_samples),
        'BedroomAbvGr': np.random.randint(1, 6, n_samples)
    })
    
    # Create realistic target variable
    sample_data['SalePrice'] = (
        sample_data['GrLivArea'] * 120 +
        sample_data['OverallQual'] * 15000 +
        (2024 - sample_data['YearBuilt']) * -200 +
        sample_data['TotalBsmtSF'] * 50 +
        sample_data['GarageCars'] * 8000 +
        sample_data['FullBath'] * 7000 +
        sample_data['BedroomAbvGr'] * 3000 +
        np.random.normal(0, 20000, n_samples)
    )
    
    # Ensure positive prices
    sample_data['SalePrice'] = np.abs(sample_data['SalePrice']) + 50000
    
    df_processed = sample_data
    print(f"✅ Created sample data: {df_processed.shape}")

print(f"\n🎯 STARTING COMPLETE MODEL SAVING WITH ALL GRADIENT BOOSTING MODELS")
print("=" * 70)

# Initialize model saver
saver = CompleteModelSaver()

# Prepare data
print(f"\n📊 PREPARING DATA")
feature_columns = ['GrLivArea', 'OverallQual', 'YearBuilt', 'TotalBsmtSF', 
                  'GarageCars', 'FullBath', 'BedroomAbvGr']

# Check available features
available_features = [col for col in feature_columns if col in df_processed.columns]
X = df_processed[available_features]
y = df_processed['SalePrice']

print(f"✅ Data prepared: {X.shape[0]} samples, {X.shape[1]} features")
print(f"✅ Features: {available_features}")

# Create and train all models
X_train, X_test, y_train, y_test = saver.create_all_models(X, y)

# Save all models
total_saved = saver.save_all_models()

# Display comparison
comparison_df = saver.display_model_comparison()

# ===== FINAL SUMMARY =====

print(f"\n🎉 COMPLETE MODEL SAVING FINISHED!")
print("=" * 50)

print(f"📊 SUMMARY:")
print(f"  • Total models trained: {len(saver.models)}")
print(f"  • XGBoost included: {'✅ Yes' if XGBOOST_AVAILABLE else '❌ No'}")
print(f"  • LightGBM included: {'✅ Yes' if LIGHTGBM_AVAILABLE else '❌ No'}")
print(f"  • CatBoost included: {'✅ Yes' if CATBOOST_AVAILABLE else '❌ No'}")
print(f"  • Files saved: {total_saved}")
print(f"  • Errors encountered: {len(saver.errors)}")

if saver.errors:
    print(f"\n⚠️ ERRORS (if any):")
    for error in saver.errors[:3]:
        print(f"  • {error}")

print(f"\n📁 ALL MODELS SAVED TO: saved_models/")
print(f"✅ XGBoost, LightGBM, and CatBoost models successfully created and saved!")
print(f"🚀 All models ready for deployment and comparison!")
print("=" * 50)
