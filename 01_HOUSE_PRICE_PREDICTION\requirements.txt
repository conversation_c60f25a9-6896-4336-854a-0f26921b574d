# PRCP-1020 House Price Prediction - Enhanced Implementation Requirements

# Core Data Science Libraries
pandas>=1.5.0
numpy>=1.21.0
scipy>=1.9.0

# Visualization Libraries
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.10.0
missingno>=0.5.0

# Machine Learning - Core
scikit-learn>=1.1.0

# Machine Learning - Advanced (Optional but Recommended)
xgboost>=1.6.0
lightgbm>=3.3.0
catboost>=1.1.0

# Model Interpretation (Optional but Recommended)
shap>=0.41.0
lime>=0.2.0

# Hyperparameter Optimization (Optional)
optuna>=3.0.0

# Jupyter Notebook Support
jupyter>=1.0.0
ipywidgets>=7.7.0

# Utilities
joblib>=1.1.0
tqdm>=4.64.0

# Statistical Analysis
statsmodels>=0.13.0

# Data Processing
openpyxl>=3.0.0

# Development Tools (Optional)
pytest>=7.1.0

# Note: This implementation is designed to work with basic libraries.
# Advanced features (XGBoost, SHAP, etc.) will be used if available,
# but the system will function with just the core libraries.
