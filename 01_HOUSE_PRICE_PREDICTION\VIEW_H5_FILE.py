# ===== H5 FILE VIEWER FOR SATELLITE SECTION =====
# Use this code to inspect and view .h5 files

import h5py
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import os
from pathlib import Path

print("📁 H5 FILE VIEWER - SATELLITE SECTION")
print("=" * 50)

# Find .h5 files in current directory
h5_files = []
for file in os.listdir('.'):
    if file.endswith('.h5'):
        h5_files.append(file)

if not h5_files:
    print("❌ No .h5 files found in current directory")
    print("\n🔍 Searching in subdirectories...")
    
    # Search in common subdirectories
    search_dirs = ['saved_models', 'models', 'data', 'output']
    for search_dir in search_dirs:
        if os.path.exists(search_dir):
            for file in os.listdir(search_dir):
                if file.endswith('.h5'):
                    h5_files.append(os.path.join(search_dir, file))
    
    if not h5_files:
        print("❌ No .h5 files found")
        print("\n💡 Please specify the exact path to your .h5 file:")
        print("h5_file_path = 'path/to/your/file.h5'")
    else:
        print(f"✅ Found {len(h5_files)} .h5 files in subdirectories")
else:
    print(f"✅ Found {len(h5_files)} .h5 files in current directory")

if h5_files:
    print(f"\n📋 AVAILABLE .H5 FILES:")
    for i, file in enumerate(h5_files, 1):
        file_size = os.path.getsize(file) / (1024*1024)  # Size in MB
        print(f"  {i}. {file} ({file_size:.2f} MB)")
    
    # Use the first .h5 file found (or specify manually)
    h5_file_path = h5_files[0]  # Change index if you want a different file
    
    print(f"\n🔍 INSPECTING: {h5_file_path}")
    print("-" * 40)
    
    try:
        # Method 1: Check if it's a Keras/TensorFlow model
        try:
            import tensorflow as tf
            print("🤖 CHECKING IF IT'S A TENSORFLOW/KERAS MODEL:")
            
            model = tf.keras.models.load_model(h5_file_path)
            print("✅ Successfully loaded as Keras model!")
            
            # Display model information
            print(f"\n📊 MODEL SUMMARY:")
            print(f"  • Model type: {type(model).__name__}")
            print(f"  • Total parameters: {model.count_params():,}")
            print(f"  • Input shape: {model.input_shape}")
            print(f"  • Output shape: {model.output_shape}")
            
            # Display model architecture
            print(f"\n🏗️ MODEL ARCHITECTURE:")
            model.summary()
            
            # Plot model architecture
            try:
                tf.keras.utils.plot_model(model, to_file='model_architecture.png', 
                                        show_shapes=True, show_layer_names=True)
                print("✅ Model architecture saved as 'model_architecture.png'")
            except:
                print("⚠️ Could not create model architecture plot")
            
        except Exception as e:
            print(f"⚠️ Not a Keras model or TensorFlow not available: {e}")
            
            # Method 2: Inspect as HDF5 file
            print(f"\n📁 INSPECTING AS HDF5 FILE:")
            
            def inspect_h5_file(file_path):
                """Recursively inspect HDF5 file structure"""
                
                def print_structure(name, obj):
                    """Print the structure of HDF5 objects"""
                    indent = "  " * (name.count('/'))
                    
                    if isinstance(obj, h5py.Dataset):
                        print(f"{indent}📄 Dataset: {name}")
                        print(f"{indent}   • Shape: {obj.shape}")
                        print(f"{indent}   • Data type: {obj.dtype}")
                        print(f"{indent}   • Size: {obj.size} elements")
                        
                        # Show sample data for small datasets
                        if obj.size < 100:
                            print(f"{indent}   • Data preview: {obj[...]}")
                        elif len(obj.shape) <= 2 and obj.shape[0] < 20:
                            print(f"{indent}   • First few values: {obj[:min(5, obj.shape[0])]}")
                    
                    elif isinstance(obj, h5py.Group):
                        print(f"{indent}📁 Group: {name}")
                        print(f"{indent}   • Contains {len(obj.keys())} items")
                        if obj.attrs:
                            print(f"{indent}   • Attributes: {dict(obj.attrs)}")
                
                with h5py.File(file_path, 'r') as f:
                    print(f"📊 HDF5 FILE STRUCTURE:")
                    print(f"  • File: {file_path}")
                    print(f"  • Root groups: {list(f.keys())}")
                    
                    # Recursively visit all objects
                    f.visititems(print_structure)
                    
                    # Show root attributes
                    if f.attrs:
                        print(f"\n🏷️ ROOT ATTRIBUTES:")
                        for key, value in f.attrs.items():
                            print(f"  • {key}: {value}")
            
            inspect_h5_file(h5_file_path)
            
            # Method 3: Try to extract data
            print(f"\n📊 EXTRACTING DATA FROM H5 FILE:")
            
            with h5py.File(h5_file_path, 'r') as f:
                # Look for common dataset names
                common_names = ['data', 'features', 'weights', 'model_weights', 'satellite_features']
                
                extracted_data = {}
                
                def extract_datasets(name, obj):
                    if isinstance(obj, h5py.Dataset):
                        try:
                            data = obj[...]
                            extracted_data[name] = data
                            print(f"  ✅ Extracted: {name} (shape: {data.shape})")
                        except Exception as e:
                            print(f"  ❌ Could not extract {name}: {e}")
                
                f.visititems(extract_datasets)
                
                # Convert to pandas DataFrame if possible
                if extracted_data:
                    print(f"\n📈 CREATING DATAFRAMES:")
                    
                    for name, data in extracted_data.items():
                        try:
                            if len(data.shape) == 2:  # 2D data can be DataFrame
                                df = pd.DataFrame(data)
                                print(f"  ✅ {name} → DataFrame {df.shape}")
                                print(f"     • Columns: {df.shape[1]}")
                                print(f"     • Rows: {df.shape[0]}")
                                
                                # Show first few rows
                                print(f"     • Preview:")
                                print(df.head().to_string(max_cols=5))
                                
                                # Save as CSV
                                csv_filename = f"{name.replace('/', '_')}_data.csv"
                                df.to_csv(csv_filename, index=False)
                                print(f"     • Saved as: {csv_filename}")
                                
                            elif len(data.shape) == 1:  # 1D data
                                print(f"  ✅ {name} → 1D Array (length: {len(data)})")
                                print(f"     • Sample values: {data[:10]}")
                                
                            else:  # Multi-dimensional data
                                print(f"  ✅ {name} → {len(data.shape)}D Array (shape: {data.shape})")
                                
                        except Exception as e:
                            print(f"  ❌ Could not process {name}: {e}")
    
    except Exception as e:
        print(f"❌ Error reading H5 file: {e}")
        print("\n💡 The file might be corrupted or in a different format")

else:
    print("\n💡 TO MANUALLY SPECIFY A FILE:")
    print("Replace the file path below and run:")
    print("""
# Specify your .h5 file path
h5_file_path = "your_file.h5"  # Replace with actual path

# Then run the inspection code above
""")

print(f"\n🛠️ ADDITIONAL TOOLS TO VIEW H5 FILES:")
print("1. 📊 HDFView (GUI): https://www.hdfgroup.org/downloads/hdfview/")
print("2. 🐍 h5py library: pip install h5py")
print("3. 📈 Pandas: pd.read_hdf('file.h5')")
print("4. 🔬 HDF5 command line tools: h5dump, h5ls")

print("\n" + "=" * 50)
