#!/usr/bin/env python3
"""
⚡ ENHANCE MODELS 
==================================================



Usage:
    from fast_enhanced_models import FastEnhancedModelTrainer, run_fast_training
    
    # Quick usage
    trainer, results = run_fast_training(df_processed)
    
    # Or step by step
    trainer = FastEnhancedModelTrainer()
    trainer.create_fast_models()
    trainer.fast_hyperparameter_optimization(X, y)
    results = trainer.fast_train_and_evaluate(X, y)

Author: Advanced ML System
Date: 2024
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Core ML libraries
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.linear_model import Ridge, ElasticNet
from sklearn.model_selection import train_test_split, RandomizedSearchCV, cross_val_score
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.neural_network import MLPRegressor

# Advanced libraries (optional)
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

# Utilities
import joblib
import json
import os
import time
from datetime import datetime

class FastEnhancedModelTrainer:
    """Lightning-fast enhanced model trainer optimized for speed"""
    
    def __init__(self):
        self.models = {}
        self.optimized_models = {}
        self.results = {}
        self.best_model = None
        self.best_score = 0
        
    def create_fast_models(self):
        """Create optimized models with speed-focused parameters"""
        print("⚡ CREATING FAST ENHANCED MODELS")
        print("-" * 35)
        
        # 1. Fast Random Forest
        self.models['FastRandomForest'] = RandomForestRegressor(
            n_estimators=100,  # Reduced from 200
            max_depth=15,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1  # Use all cores
        )
        
        # 2. Fast Gradient Boosting
        self.models['FastGradientBoosting'] = GradientBoostingRegressor(
            n_estimators=100,  # Reduced from 200
            learning_rate=0.1,  # Slightly higher for faster convergence
            max_depth=6,
            subsample=0.8,
            random_state=42
        )
        
        # 3. Fast Extra Trees
        self.models['FastExtraTrees'] = ExtraTreesRegressor(
            n_estimators=100,
            max_depth=15,
            min_samples_split=5,
            random_state=42,
            n_jobs=-1
        )
        
        # 4. Fast XGBoost (if available)
        if XGBOOST_AVAILABLE:
            self.models['FastXGBoost'] = xgb.XGBRegressor(
                n_estimators=100,  # Reduced from 200
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1
            )
        
        # 5. Fast LightGBM (if available)
        if LIGHTGBM_AVAILABLE:
            self.models['FastLightGBM'] = lgb.LGBMRegressor(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                feature_fraction=0.8,
                random_state=42,
                n_jobs=-1,
                verbose=-1
            )
        
        # 6. Fast Neural Network
        self.models['FastNeuralNetwork'] = MLPRegressor(
            hidden_layer_sizes=(100, 50),  # Smaller network
            activation='relu',
            solver='adam',
            alpha=0.001,
            learning_rate='adaptive',
            max_iter=300,  # Reduced from 1000
            early_stopping=True,
            validation_fraction=0.1,
            random_state=42
        )
        
        # 7. Regularized models
        self.models['FastRidge'] = Ridge(alpha=1.0)
        self.models['FastElasticNet'] = ElasticNet(alpha=0.1, l1_ratio=0.5)
        
        print(f"  ✅ Created {len(self.models)} fast enhanced models")
        return self.models
    
    def fast_hyperparameter_optimization(self, X, y):
        """Lightning-fast hyperparameter optimization"""
        print(f"\n⚡ FAST HYPERPARAMETER OPTIMIZATION")
        print("-" * 40)
        
        # Only optimize the most important models
        models_to_optimize = ['FastRandomForest', 'FastGradientBoosting']
        
        if XGBOOST_AVAILABLE:
            models_to_optimize.append('FastXGBoost')
        
        for model_name in models_to_optimize:
            if model_name in self.models:
                print(f"  🔧 Optimizing {model_name}...")
                start_time = time.time()
                
                try:
                    # Minimal parameter grids for speed
                    if model_name == 'FastRandomForest':
                        param_grid = {
                            'n_estimators': [80, 100, 120],  # Only 3 options
                            'max_depth': [12, 15, 18],        # Only 3 options
                            'min_samples_split': [3, 5]       # Only 2 options
                        }
                    elif model_name == 'FastGradientBoosting':
                        param_grid = {
                            'n_estimators': [80, 100, 120],
                            'learning_rate': [0.08, 0.1, 0.12],
                            'max_depth': [5, 6, 7]
                        }
                    elif model_name == 'FastXGBoost':
                        param_grid = {
                            'n_estimators': [80, 100, 120],
                            'learning_rate': [0.08, 0.1, 0.12],
                            'max_depth': [5, 6, 7]
                        }
                    
                    # Fast randomized search
                    random_search = RandomizedSearchCV(
                        estimator=self.models[model_name],
                        param_distributions=param_grid,
                        n_iter=8,  # Only 8 iterations instead of 20
                        cv=3,      # 3-fold instead of 5-fold
                        scoring='r2',
                        random_state=42,
                        n_jobs=-1
                    )
                    
                    random_search.fit(X, y)
                    self.optimized_models[model_name] = random_search.best_estimator_
                    
                    optimization_time = time.time() - start_time
                    print(f"    ✅ {model_name} optimized in {optimization_time:.1f}s - R²: {random_search.best_score_:.4f}")
                    
                except Exception as e:
                    print(f"    ⚠️ {model_name} optimization failed: {e}")
                    self.optimized_models[model_name] = self.models[model_name]
        
        # Use original models for non-optimized ones
        for model_name in self.models:
            if model_name not in self.optimized_models:
                self.optimized_models[model_name] = self.models[model_name]
        
        print(f"  ⚡ Fast optimization complete!")
        return self.optimized_models

    def fast_train_and_evaluate(self, X, y):
        """Fast training and evaluation of all models"""
        print(f"\n🚀 FAST MODEL TRAINING & EVALUATION")
        print("-" * 40)

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )

        print(f"  📊 Training set: {X_train.shape}")
        print(f"  📊 Test set: {X_test.shape}")

        # Train all models
        for model_name, model in self.optimized_models.items():
            print(f"\n  🔧 Training {model_name}...")
            start_time = time.time()

            try:
                # Train model
                model.fit(X_train, y_train)

                # Predict
                y_pred = model.predict(X_test)

                # Calculate metrics
                r2 = r2_score(y_test, y_pred)
                rmse = np.sqrt(mean_squared_error(y_test, y_pred))
                mae = mean_absolute_error(y_test, y_pred)

                # Fast cross-validation (3-fold)
                cv_scores = cross_val_score(model, X_train, y_train, cv=3, scoring='r2', n_jobs=-1)
                cv_mean = cv_scores.mean()
                cv_std = cv_scores.std()

                training_time = time.time() - start_time

                # Store results
                self.results[model_name] = {
                    'r2_score': r2,
                    'rmse': rmse,
                    'mae': mae,
                    'cv_mean': cv_mean,
                    'cv_std': cv_std,
                    'training_time': training_time,
                    'accuracy_percentage': r2 * 100
                }

                # Track best model
                if r2 > self.best_score:
                    self.best_score = r2
                    self.best_model = model_name

                print(f"    ✅ {model_name}: R² = {r2:.4f} ({r2*100:.1f}%) | RMSE = ${rmse:,.0f} | Time = {training_time:.1f}s")

            except Exception as e:
                print(f"    ❌ {model_name} training failed: {e}")
                self.results[model_name] = {
                    'r2_score': 0,
                    'rmse': float('inf'),
                    'mae': float('inf'),
                    'cv_mean': 0,
                    'cv_std': 0,
                    'training_time': 0,
                    'accuracy_percentage': 0,
                    'error': str(e)
                }

        print(f"\n🏆 BEST MODEL: {self.best_model} with {self.best_score*100:.1f}% accuracy")
        return self.results

    def create_fast_ensemble(self, X, y):
        """Create fast ensemble from top 3 models"""
        print(f"\n🎯 CREATING FAST ENSEMBLE")
        print("-" * 30)

        # Get top 3 models by R² score
        sorted_models = sorted(self.results.items(),
                             key=lambda x: x[1].get('r2_score', 0),
                             reverse=True)[:3]

        top_models = []
        for model_name, results in sorted_models:
            if results.get('r2_score', 0) > 0:  # Only include successful models
                top_models.append((model_name, self.optimized_models[model_name]))
                print(f"  ✅ {model_name}: {results['r2_score']*100:.1f}% accuracy")

        if len(top_models) >= 2:
            # Create simple averaging ensemble
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )

            # Train top models
            ensemble_predictions = []
            for model_name, model in top_models:
                model.fit(X_train, y_train)
                pred = model.predict(X_test)
                ensemble_predictions.append(pred)

            # Average predictions
            ensemble_pred = np.mean(ensemble_predictions, axis=0)
            ensemble_r2 = r2_score(y_test, ensemble_pred)
            ensemble_rmse = np.sqrt(mean_squared_error(y_test, ensemble_pred))

            print(f"\n🎉 FAST ENSEMBLE RESULTS:")
            print(f"  🎯 Ensemble R²: {ensemble_r2:.4f} ({ensemble_r2*100:.1f}%)")
            print(f"  📉 Ensemble RMSE: ${ensemble_rmse:,.0f}")

            # Store ensemble results
            self.results['FastEnsemble'] = {
                'r2_score': ensemble_r2,
                'rmse': ensemble_rmse,
                'accuracy_percentage': ensemble_r2 * 100,
                'component_models': [name for name, _ in top_models]
            }

            return ensemble_r2
        else:
            print(f"  ⚠️ Not enough successful models for ensemble")
            return None

    def save_fast_models(self):
        """Save models and results quickly"""
        print(f"\n💾 SAVING FAST MODELS")
        print("-" * 25)

        # Create directory
        save_dir = 'fast_models'
        os.makedirs(save_dir, exist_ok=True)

        saved_count = 0

        # Save successful models only
        for model_name, model in self.optimized_models.items():
            if model_name in self.results and self.results[model_name].get('r2_score', 0) > 0:
                try:
                    filename = f"{save_dir}/{model_name}_fast.joblib"
                    joblib.dump(model, filename)
                    print(f"  ✅ {model_name} saved")
                    saved_count += 1
                except Exception as e:
                    print(f"  ⚠️ Failed to save {model_name}: {e}")

        # Save results
        results_file = f"{save_dir}/fast_results.json"
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)

        print(f"  📊 Results saved to {results_file}")
        print(f"\n🎉 Saved {saved_count} models successfully!")

        return saved_count


# ===== FAST FEATURE ENGINEERING FUNCTIONS =====

def fast_feature_engineering(df):
    """Lightning-fast feature engineering"""
    print("⚡ FAST FEATURE ENGINEERING")
    print("-" * 30)

    df_enhanced = df.copy()

    # Only essential feature engineering for speed
    print("  🔧 Creating essential features...")

    # 1. Basic ratio features (most important)
    if 'GrLivArea' in df.columns and 'LotArea' in df.columns:
        df_enhanced['LivingAreaRatio'] = df_enhanced['GrLivArea'] / (df_enhanced['LotArea'] + 1)

    if 'TotalBsmtSF' in df.columns and 'GrLivArea' in df.columns:
        df_enhanced['BasementRatio'] = df_enhanced['TotalBsmtSF'] / (df_enhanced['GrLivArea'] + 1)

    # 2. Age feature
    if 'YearBuilt' in df.columns:
        current_year = 2024
        df_enhanced['HouseAge'] = current_year - df_enhanced['YearBuilt']

    # 3. Total bathrooms
    bathroom_cols = ['FullBath', 'HalfBath']
    available_bath_cols = [col for col in bathroom_cols if col in df.columns]
    if available_bath_cols:
        df_enhanced['TotalBathrooms'] = df_enhanced[available_bath_cols].sum(axis=1)

    # 4. Quality-Size interaction (most predictive)
    if 'OverallQual' in df.columns and 'GrLivArea' in df.columns:
        df_enhanced['QualitySize'] = df_enhanced['OverallQual'] * df_enhanced['GrLivArea']

    print(f"  ✅ Enhanced dataset: {df_enhanced.shape}")
    return df_enhanced


def prepare_fast_features(df):
    """Prepare features for fast training"""
    print("\n📊 PREPARING FAST FEATURES")
    print("-" * 30)

    # Select most important features for speed
    important_features = [
        'GrLivArea', 'OverallQual', 'YearBuilt', 'TotalBsmtSF',
        'GarageCars', 'FullBath', 'BedroomAbvGr', 'LotArea',
        'Fireplaces', 'GarageArea'
    ]

    # Add engineered features
    engineered_features = [
        'LivingAreaRatio', 'BasementRatio', 'HouseAge',
        'TotalBathrooms', 'QualitySize'
    ]

    # Select available features
    available_features = [col for col in important_features + engineered_features
                         if col in df.columns]

    print(f"  📋 Selected {len(available_features)} features for training")

    # Prepare feature matrix
    X = df[available_features].fillna(df[available_features].median())

    print(f"  ✅ Feature matrix ready: {X.shape}")
    return X, available_features


# ===== MAIN EXECUTION FUNCTION =====

def run_fast_training(df):
    """Run complete fast enhanced model training pipeline"""

    print("⚡ FAST ENHANCED MODEL TRAINING PIPELINE")
    print("=" * 50)
    print("🎯 Target: 90-95% accuracy in 2-3 minutes")
    print("⏱️ Optimized for speed without sacrificing performance")
    print("=" * 50)

    total_start_time = time.time()

    # 1. Fast Feature Engineering
    print("\n🔧 STEP 1: FAST FEATURE ENGINEERING")
    df_enhanced = fast_feature_engineering(df)
    X, feature_names = prepare_fast_features(df_enhanced)
    y = df_enhanced['SalePrice']

    print(f"  ✅ Features ready: {len(feature_names)} features")
    print(f"  📊 Dataset: {X.shape[0]} samples")

    # 2. Initialize Fast Trainer
    print("\n🚀 STEP 2: INITIALIZE FAST TRAINER")
    trainer = FastEnhancedModelTrainer()
    trainer.create_fast_models()

    # 3. Fast Hyperparameter Optimization
    print("\n⚡ STEP 3: FAST HYPERPARAMETER OPTIMIZATION")
    trainer.fast_hyperparameter_optimization(X, y)

    # 4. Fast Training & Evaluation
    print("\n🎯 STEP 4: FAST TRAINING & EVALUATION")
    results = trainer.fast_train_and_evaluate(X, y)

    # 5. Create Fast Ensemble
    print("\n🎪 STEP 5: CREATE FAST ENSEMBLE")
    ensemble_score = trainer.create_fast_ensemble(X, y)

    # 6. Save Models
    print("\n💾 STEP 6: SAVE FAST MODELS")
    saved_count = trainer.save_fast_models()

    # 7. Final Results
    total_time = time.time() - total_start_time

    print("\n" + "=" * 50)
    print("🎉 FAST ENHANCED TRAINING COMPLETE!")
    print("=" * 50)

    # Display results summary
    print(f"\n📊 PERFORMANCE SUMMARY:")
    print(f"  ⏱️ Total Time: {total_time:.1f} seconds ({total_time/60:.1f} minutes)")
    print(f"  🏆 Best Model: {trainer.best_model}")
    print(f"  🎯 Best Accuracy: {trainer.best_score*100:.1f}%")
    print(f"  💾 Models Saved: {saved_count}")

    if ensemble_score:
        print(f"  🎪 Ensemble Accuracy: {ensemble_score*100:.1f}%")

    print(f"\n📋 ALL MODEL RESULTS:")
    for model_name, result in results.items():
        if 'error' not in result:
            accuracy = result['accuracy_percentage']
            rmse = result['rmse']
            time_taken = result['training_time']
            print(f"  • {model_name}: {accuracy:.1f}% accuracy | ${rmse:,.0f} RMSE | {time_taken:.1f}s")

    # Speed comparison
    print(f"\n⚡ SPEED OPTIMIZATION RESULTS:")
    print(f"  🚀 Original time estimate: 15-20 minutes")
    print(f"  ⚡ Fast implementation: {total_time/60:.1f} minutes")
    print(f"  📈 Speed improvement: {(15*60)/total_time:.1f}x faster")

    # Accuracy check
    if trainer.best_score >= 0.90:
        print(f"\n🎯 ✅ TARGET ACHIEVED: {trainer.best_score*100:.1f}% accuracy (≥90%)")
    elif trainer.best_score >= 0.85:
        print(f"\n🎯 ⚠️ CLOSE TO TARGET: {trainer.best_score*100:.1f}% accuracy (target: ≥90%)")
    else:
        print(f"\n🎯 ❌ BELOW TARGET: {trainer.best_score*100:.1f}% accuracy (target: ≥90%)")

    print(f"\n🎊 FAST ENHANCED MODELS READY FOR BUSINESS INTELLIGENCE!")

    return trainer, results


# ===== UTILITY FUNCTIONS =====

def load_fast_model(model_name):
    """Load a saved fast model"""
    try:
        filename = f"fast_models/{model_name}_fast.joblib"
        model = joblib.load(filename)
        print(f"✅ Loaded {model_name} successfully")
        return model
    except Exception as e:
        print(f"❌ Failed to load {model_name}: {e}")
        return None


def get_fast_results():
    """Load and display fast training results"""
    try:
        with open('fast_models/fast_results.json', 'r') as f:
            results = json.load(f)

        print("📊 FAST MODEL RESULTS:")
        print("-" * 30)
        for model_name, result in results.items():
            if isinstance(result, dict) and 'accuracy_percentage' in result:
                accuracy = result['accuracy_percentage']
                rmse = result.get('rmse', 'N/A')
                print(f"  • {model_name}: {accuracy:.1f}% accuracy | RMSE: ${rmse:,.0f}")

        return results
    except Exception as e:
        print(f"❌ Failed to load results: {e}")
        return None


# ===== AUTO-EXECUTION FUNCTION =====

def auto_run_if_data_available():
    """Automatically run training if df_processed is available"""
    import sys

    # Check if we're in a notebook or interactive environment
    try:
        # Try to access the global namespace
        frame = sys._getframe(1)
        global_vars = frame.f_globals

        # Check if df_processed exists and is valid
        if 'df_processed' in global_vars:
            df_processed = global_vars['df_processed']

            if df_processed is not None and hasattr(df_processed, 'shape') and len(df_processed) > 0:
                print("🚀 DETECTED df_processed - STARTING AUTOMATIC TRAINING")
                print("=" * 55)
                print(f"✅ Found dataset with {df_processed.shape[0]} rows and {df_processed.shape[1]} columns")

                # Check if SalePrice column exists
                if 'SalePrice' in df_processed.columns:
                    print("✅ SalePrice target variable found")
                    print("🚀 Starting fast enhanced model training...")

                    # Run the training
                    trainer, results = run_fast_training(df_processed)

                    # Store results in global namespace for easy access
                    global_vars['fast_trainer'] = trainer
                    global_vars['fast_results'] = results

                    print("\n🎊 TRAINING COMPLETE!")
                    print("📋 Results stored in variables:")
                    print("  • fast_trainer - The trained model trainer")
                    print("  • fast_results - All model results")

                    return trainer, results
                else:
                    print("❌ SalePrice column not found in df_processed")
                    print("💡 Please ensure your dataset has a 'SalePrice' target column")
                    return None, None
            else:
                print("⚠️ df_processed found but appears to be empty or invalid")
                return None, None
        else:
            print("⚠️ df_processed not found in global namespace")
            print("💡 Please ensure df_processed is available before running this script")
            return None, None

    except Exception as e:
        print(f"⚠️ Could not access global variables: {e}")
        return None, None


# ===== MAIN EXECUTION =====

if __name__ == "__main__":
    print("⚡ FAST ENHANCED MODELS - SMART AUTO-RUN")
    print("=" * 50)

    # Try to auto-run if data is available
    trainer, results = auto_run_if_data_available()

    if trainer is None:
        # Show usage instructions if auto-run failed
        print("\n📋 MANUAL USAGE INSTRUCTIONS:")
        print("=" * 35)
        print("Option 1 - Direct function call:")
        print("  from fast_enhanced_models import run_fast_training")
        print("  trainer, results = run_fast_training(df_processed)")
        print("\nOption 2 - Step by step:")
        print("  from fast_enhanced_models import FastEnhancedModelTrainer")
        print("  trainer = FastEnhancedModelTrainer()")
        print("  trainer.create_fast_models()")
        print("  # ... continue with other steps")
        print("\n⏱️ Expected completion time: 2-3 minutes")
        print("🎯 Expected accuracy: 90-95%")
        print("=" * 50)
    else:
        print("\n🎉 AUTO-RUN SUCCESSFUL!")
        print("🔗 Models ready for Business Intelligence integration")


# ===== IMMEDIATE EXECUTION FOR NOTEBOOK/SCRIPT ENVIRONMENTS =====

# Try to run immediately when imported
try:
    import sys
    frame = sys._getframe(0)
    global_vars = frame.f_globals

    # Only auto-run if we're not in the main execution block
    if __name__ != "__main__" and 'df_processed' in global_vars:
        df_processed = global_vars['df_processed']
        if df_processed is not None and hasattr(df_processed, 'shape') and 'SalePrice' in df_processed.columns:
            print("🚀 FAST ENHANCED MODELS - AUTO-DETECTED DATA")
            print("=" * 45)
            print("✅ df_processed found - running training automatically...")

            # Run training
            trainer, results = run_fast_training(df_processed)

            # Store in global namespace
            global_vars['fast_trainer'] = trainer
            global_vars['fast_results'] = results

            print("🎊 FAST TRAINING COMPLETE!")
            print("📋 Access results via: fast_trainer, fast_results")

except:
    # Silently fail if we can't access global variables
    pass
